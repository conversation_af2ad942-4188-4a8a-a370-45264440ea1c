spring:
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    druid:
      url: *******************************************************************************************************************************************
      username: admin
      password: 82qFhADcQ6
      driver-class-name: com.mysql.cj.jdbc.Driver
      max-active: 50
      max-wait: 10000
      min-idle: 3
      initial-size: 5
  mvc:
    throw-exception-if-no-handler-found: true
  main:
    allow-bean-definition-overriding: true
  servlet:
    multipart:
      max-file-size: 100MB
      max-request-size: 100MB

mybatis-plus:
  mapper-locations: classpath:/mapper/*Mapper.xml
  #实体扫描，多个package用逗号或者分号分隔
  typeAliasesPackage: cn.getech.poros.ai.entity

feign:
  sentinel:
    enabled: true
  httpclient:
    enabled: true
    # 最大连接数，默认：200
    max-connections: 1000
    # 最大路由，默认：50
    max-connections-per-route: 50
    # 连接超时，默认：2000/毫秒
    connection-timeout: 3000
    # 生存时间，默认：900L
    time-to-live: 900
    time-to-live-unit: seconds

poros:
  gateway:
    gatewayUrl: http://kong.poros-platform:8001
  sername: poros-bur-application
  #系统标识，读取maven property
  syscode: '@syscode@'
  # swagger配置
  swagger:
    version: 1.0
    title: test
    #rest响应aop
    base-package: cn.getech.poros.ai
  rest-aop:
    base-packages: cn.getech.poros.ai
  permission:
    enabled: false
