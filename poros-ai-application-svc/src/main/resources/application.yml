spring:
  profiles:
    active: dev



redis:
  database: smt
  key:
    admin: 'user:admin'
    resourceList: 'user:resourceList'
    token: 'user:token'
  expire:
    common: 604800 # 24小时*7



secure:
  ignored:
    urls: #安全路径白名单
      - /**/doc.html/
      - /swagger-ui.html
      - /swagger-resources/**
      - /swagger/**
      - /**/v2/api-docs
      - /**/*.js
      - /**/*.css
      - /**/*.jpg
      - /**/*.png
      - /**/*.ico
      - /webjars/springfox-swagger-ui/**
      - /druid/**
      - /actuator/**
      - /actuator/**
      - /sso/**
      - /**/imageUpload/**
      - /**/user/login/**
      - /**/user/register/**

jwt:
  tokenHeader: Authorization #JWT存储的请求头
  secret: bur-portal-secret #JWT加解密使用的密钥
  expiration: 604800 #JWT的超期限时间(60*60*24*7)
  tokenHead: 'Bearer '  #JWT负载中拿到开头