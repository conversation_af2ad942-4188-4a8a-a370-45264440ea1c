<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.getech.poros.permission.mapper.UserMapper">


    <select id="userList" resultType="cn.getech.poros.permission.dto.user.UserListDto"
            parameterType="cn.getech.poros.permission.dto.user.UserListParam">
        SELECT u.id userId,u.user_name,u.id_card_no,u.level,u.user_account,u.create_time, r.id roleId, r.role_name
        FROM
        `um_user` u LEFT JOIN um_user_role ur ON u.id = ur.user_id
        LEFT JOIN um_role r ON ur.role_id = r.id
        where
        u.user_name != 'admin'
        <if test="userName != '' and userName != null">
            and u.user_name like concat('%',#{userName},'%')
        </if>
        <if test="roleId != null">
            and r.id =#{roleId}
        </if>
        <if test="userIds != null and userIds.size() > 0">
            and u.id IN
            <foreach collection="userIds" index="index" item="id" separator="," close=")" open="(">
                 #{id}
            </foreach>
        </if>
        order by u.update_time desc
    </select>

    <select id="roleList" resultType="cn.getech.poros.permission.dto.user.RoleListDto"
            parameterType="cn.getech.poros.permission.dto.user.RoleListParam">
        SELECT *
        FROM um_role
        where 1=1
        <if test="roleName != '' and roleName != null">
            and role_name like concat('%',#{roleName},'%')
        </if>
        order by update_time desc
    </select>

    <select id="getUserNameList" resultType="java.lang.String"
            parameterType="cn.getech.poros.permission.dto.user.RoleWithUsersAssociationParam">
        SELECT u.user_name
        FROM
        `um_user` u LEFT JOIN um_user_role ur ON u.id = ur.user_id
        LEFT JOIN um_role r ON ur.role_id = r.id
        where
        u.user_name != 'admin'
        <if test="userIds == null">
            and r.id =#{roleId}
        </if>
        <if test="userIds.size() == 0">
            and r.id =#{roleId}
        </if>
        <if test="userIds != null and userIds.size() > 0">
            and (r.id =#{roleId} or u.id IN
            <foreach collection="userIds" index="index" item="id" separator="," close=")" open="(">
                #{id}
            </foreach>)
        </if>
    </select>

    <select id="menuListByRoleId" parameterType="java.lang.Long" resultType="cn.getech.poros.permission.dto.user.MenuDto">
        select m.*
        from
        um_menu m LEFT JOIN um_role_menu rm
        on m.id = rm.menu_id where 1=1
        <if test="roleId != '' and roleId != null">
            and rm.role_id =#{roleId}
        </if>
        order by sequence
    </select>

    <select id="menuList" parameterType="java.lang.String" resultType="cn.getech.poros.permission.dto.user.MenuDto">
        select *
        from
        um_menu where 1=1
        <if test="menuName != '' and menuName != null">
            and menu_name =#{menuName}
        </if>
        order by sequence
    </select>

</mapper>
