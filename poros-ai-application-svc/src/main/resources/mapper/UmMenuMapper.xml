<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.getech.poros.permission.mapper.UmMenuMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.getech.poros.permission.entity.UmMenu">
    <result column="id" property="id" />
    <result column="create_time" property="createTime" />
    <result column="update_time" property="updateTime" />
    <result column="create_by" property="createBy" />
    <result column="update_by" property="updateBy" />
    <result column="remark" property="remark" />
        <result column="parent_menu_id" property="parentMenuId" />
        <result column="sequence" property="sequence" />
        <result column="menu_code" property="menuCode" />
        <result column="menu_name" property="menuName" />
        <result column="menu_url" property="menuUrl" />
        <result column="icon" property="icon" />
        <result column="mask" property="mask" />
        <result column="hidden" property="hidden" />
        <result column="resource_type" property="resourceType" />
        <result column="component_path" property="componentPath" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        create_time,
        update_time,
        create_by,
        update_by,
        remark,
        parent_menu_id, sequence, menu_code, menu_name, menu_url, icon, mask, hidden, resource_type, component_path
    </sql>

</mapper>
