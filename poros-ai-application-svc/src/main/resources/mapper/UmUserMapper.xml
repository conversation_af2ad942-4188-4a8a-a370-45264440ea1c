<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.getech.poros.permission.mapper.UmUserMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.getech.poros.permission.entity.UmUser">
    <result column="id" property="id" />
    <result column="create_time" property="createTime" />
    <result column="update_time" property="updateTime" />
    <result column="create_by" property="createBy" />
    <result column="update_by" property="updateBy" />
    <result column="remark" property="remark" />
        <result column="user_code" property="userCode" />
        <result column="user_name" property="userName" />
        <result column="id_card_no" property="idCardNo" />
        <result column="user_account" property="userAccount" />
        <result column="user_password" property="userPassword" />
        <result column="level" property="level" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        create_time,
        update_time,
        create_by,
        update_by,
        remark,
        user_code, user_name, id_card_no, user_account, user_password, level
    </sql>

</mapper>
