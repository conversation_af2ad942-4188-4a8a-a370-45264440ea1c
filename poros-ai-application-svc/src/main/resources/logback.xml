<?xml version="1.0" encoding="UTF-8"?>
<configuration>

    <springProperty scope="context" name="logging.file.path" source="logging.file.path"/>

    <appender name="AuditLogAppender" class="ch.qos.logback.core.ConsoleAppender">
        <encoder class="ch.qos.logback.core.encoder.LayoutWrappingEncoder">
            <layout class="org.apache.skywalking.apm.toolkit.log.logback.v1.x.TraceIdPatternLogbackLayout">
                <pattern>
                    $$@syscode@‖poros-ai-applicationcenter-svc‖%tid‖AUDIT_LOG‖%-5level‖%d{yyyy-MM-dd HH:mm:ss.SSS}‖%thread‖%logger{36}.%M‖%msg%n
                </pattern>
            </layout>
        </encoder>
    </appender>

    <appender name="LoginLogAppender" class="ch.qos.logback.core.ConsoleAppender">
        <encoder class="ch.qos.logback.core.encoder.LayoutWrappingEncoder">
            <layout class="org.apache.skywalking.apm.toolkit.log.logback.v1.x.TraceIdPatternLogbackLayout">
                <pattern>
                    $$@syscode@‖poros-bur-application-svc‖%tid‖LOGIN_LOG‖%-5level‖%d{yyyy-MM-dd HH:mm:ss.SSS}‖%thread‖%logger{36}.%M‖%msg%n
                </pattern>
            </layout>
        </encoder>
    </appender>

    <appender name="BizLogAppender" class="ch.qos.logback.core.ConsoleAppender">
        <encoder class="ch.qos.logback.core.encoder.LayoutWrappingEncoder">
            <layout class="org.apache.skywalking.apm.toolkit.log.logback.v1.x.TraceIdPatternLogbackLayout">
                <pattern>
                    $$@syscode@‖poros-bur-application-svc‖%tid‖BIZ_LOG‖%-5level‖%d{yyyy-MM-dd HH:mm:ss.SSS}‖%thread‖%logger{36}.%M‖%msg%n
                </pattern>
            </layout>
        </encoder>
    </appender>

    <logger name="audit_log" level="info" additivity="false">
        <appender-ref ref="AuditLogAppender"/>
    </logger>

    <logger name="login_log" level="info" additivity="false">
        <appender-ref ref="LoginLogAppender"/>
    </logger>

    <!-- 按照每天生成日志文件 -->
    <appender name="File" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <Prudent>true</Prudent>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <!--日志文件输出的文件名-->
            <FileNamePattern>${logging.file.path}/application_center.%d{yyyy-MM-dd}.log</FileNamePattern>
            <!--日志文件保留天数-->
            <MaxHistory>30</MaxHistory>
        </rollingPolicy>
        <!-- 日志文件的格式 -->
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>%d{MM-dd HH:mm:ss.SSS} %-5level %logger{36} L%line : %msg%n</pattern>
        </encoder>
    </appender>

    <root level="info">
        <appender-ref ref="BizLogAppender"/>
        <appender-ref ref="File"/>
    </root>
</configuration>