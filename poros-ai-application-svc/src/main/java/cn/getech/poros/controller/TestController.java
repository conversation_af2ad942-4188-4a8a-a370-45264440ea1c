package cn.getech.poros.controller;


import cn.getech.poros.framework.common.api.RestResponse;
import io.swagger.annotations.Api;
import lombok.extern.log4j.Log4j2;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


@Log4j2
@Api(tags = "测试api")
@RestController
@RequestMapping("/test")
public class TestController {


    @PostMapping("/test")
    public RestResponse<String> test() {
        return RestResponse.ok("ok!");
    }


}
