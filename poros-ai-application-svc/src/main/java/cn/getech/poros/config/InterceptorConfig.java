package cn.getech.poros.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * Created by zxl on 2018/8/31.
 */
@Configuration
@Slf4j
public class InterceptorConfig implements WebMvcConfigurer {

    @Value(value = "${nfs.upload}")
    private String imageUpload;

    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        log.info("imageUpload:{}",imageUpload);
        //registry.addResourceHandler("/" + Constant.IMAGE_UPLOAD + "/**").addResourceLocations("file:" + imageUpload+"/");
    }

}
