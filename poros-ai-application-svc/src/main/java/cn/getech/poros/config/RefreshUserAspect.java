package cn.getech.poros.config;

import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.Signature;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.stereotype.Component;

@Slf4j
@Aspect
@Component
public class RefreshUserAspect {



    //@Before("execution(* cn.getech.poros.ai.controller.*.*(..))")
    public void before(JoinPoint joinPoint){
        System.out.println("前置通知");
        Object target = joinPoint.getTarget();//目标对象
        Signature signature = joinPoint.getSignature();//方法签名
        Object[] args = joinPoint.getArgs();//方法参数

    }
}
