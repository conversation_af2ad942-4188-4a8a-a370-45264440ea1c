package cn.getech.poros.permission.dto.user;

import cn.getech.poros.framework.common.param.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;


/**
 * <pre>
 *  返回数据模型
 * </pre>
 *
 * <AUTHOR>
 * @date 2022-06-07
 */
@Data
@ApiModel(value = "RoleListParam", description = "角色列表入参")
public class RoleListParam extends PageParam implements Serializable {

    @ApiModelProperty(value = "角色名称")
    private String roleName;


}