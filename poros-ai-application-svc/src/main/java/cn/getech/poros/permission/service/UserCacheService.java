package cn.getech.poros.permission.service;

import cn.getech.poros.permission.dto.user.MenuDto;
import cn.getech.poros.permission.dto.user.UserDto;

import java.util.List;

/**
 * 后台用户缓存操作Service
 */
public interface UserCacheService {
    /**
     * 删除后台用户缓存
     */
    void delUser(String userName);

    /**
     * 删除后台用户资源列表缓存
     */
    void delResourceList(Long roleId);

    /**
     * 当角色相关资源信息改变时删除相关后台用户缓存
     */
    void delUsersListByRole(List<String> userNames);
    /**
     * 当角色相关资源信息改变时删除相关后台用户缓存
     */
    void delResourceListByRole(List<Long> userIds);

    /**
     * 获取缓存后台用户信息
     */
    String getToken(String username);
    /**
     * 获取缓存后台用户信息
     */
    void setToken(String username,String token);
    /**
     * 获取缓存后台用户信息
     */
    UserDto getUser(String username);

    /**
     * 设置缓存后台用户信息
     */
    void setUser(UserDto userDto);

    /**
     * 获取缓存后台用户资源列表
     */
    List<MenuDto> getResourceList(Long roleId);

    /**
     * 设置缓存后台用户资源列表
     */
    void setResourceList(Long roleId, List<MenuDto> resourceList);
}
