package cn.getech.poros.permission.dto.user;

import cn.getech.poros.framework.common.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;


/**
 * <pre>
 *  返回数据模型
 * </pre>
 *
 * <AUTHOR>
 * @date 2022-06-13
 */
@Data
@ApiModel(value = "SmtRoleDto", description = "返回数据模型")
public class RoleListDto {

    @ApiModelProperty(value = "角色编号")
    @Excel(name="角色编号",cellType = Excel.ColumnType.STRING )
    private String roleCode;

    @ApiModelProperty(value = "角色名称")
    @Excel(name="角色名称",cellType = Excel.ColumnType.STRING )
    private String roleName;

    @ApiModelProperty(value = "")
    @Excel(name="",cellType = Excel.ColumnType.STRING)
    private Long id;

    @ApiModelProperty(value = "创建时间")
    @Excel(name="创建时间",cellType = Excel.ColumnType.STRING ,dateFormat="yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @ApiModelProperty(value = "备注")
    @Excel(name="备注",cellType = Excel.ColumnType.STRING )
    private String remark;

}