package cn.getech.poros.permission.dto.user;

import cn.getech.poros.framework.common.param.ApiParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <pre>
 *  新增参数
 * </pre>
 *
 * <AUTHOR>
 * @date 2022-06-08
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "UserEditPasswordParam", description = "修改用户密码入参")
public class UserEditPasswordParam extends ApiParam {


    @ApiModelProperty(value = "主键")
    private Long id;

    @ApiModelProperty(value = "用户名")
    private String userName;

    @ApiModelProperty(value = "旧密码")
    private String userPasswordOld;

    @ApiModelProperty(value = "新密码")
    private String userPasswordNew;


}