package cn.getech.poros.permission.dto.user;

import cn.getech.poros.framework.common.param.ApiParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <pre>
 *  新增参数
 * </pre>
 *
 * <AUTHOR>
 * @date 2022-08-15
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "UmMenu新增", description = "新增参数")
public class MenuAddParam extends ApiParam {

    @ApiModelProperty(value = "父级id")
    private Integer parentMenuId;
    @ApiModelProperty(value = "排序")
    private Integer sequence;
    @ApiModelProperty(value = "菜单编号")
    private String menuCode;
    @ApiModelProperty(value = "菜单名称")
    private String menuName;
    @ApiModelProperty(value = "路由地址")
    private String menuUrl;
    @ApiModelProperty(value = "菜单icon")
    private String icon;
    @ApiModelProperty(value = "权限标识")
    private String mask;
    @ApiModelProperty(value = "隐藏标识：0 false-不隐藏，1 true-隐藏")
    private String hidden;
    @ApiModelProperty(value = "资源类型（菜单/按钮等）0：菜单 1：按钮 ")
    private String resourceType;
    @ApiModelProperty(value = "组件路径")
    private String componentPath;
    @ApiModelProperty(value = "")
    private Long id;
    @ApiModelProperty(value = "备注")
    private String remark;
}