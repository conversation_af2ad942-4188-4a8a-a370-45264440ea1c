package cn.getech.poros.permission.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import cn.getech.poros.framework.common.bean.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import cn.getech.poros.framework.common.annotation.Excel;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-04
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("um_user")
public class UmUser extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * 待定
     */
    @TableField("user_code")
    private String userCode;

    /**
     * 用户名
     */
    @TableField("user_name")
    private String userName;

    /**
     * 卡号
     */
    @TableField("id_card_no")
    private String idCardNo;

    /**
     * 姓名
     */
    @TableField("user_account")
    private String userAccount;

    /**
     * 密码
     */
    @TableField("user_password")
    private String userPassword;

    /**
     * 等级
     */
    @TableField("level")
    private Integer level;


}
