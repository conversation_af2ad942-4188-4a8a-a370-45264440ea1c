package cn.getech.poros.permission.dto;

import cn.getech.poros.framework.common.param.ApiParam;
import cn.getech.poros.framework.common.param.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.Date;

/**
 * <pre>
 *  编辑参数对象
 * </pre>
 *
 * <AUTHOR>
 * @date 2022-08-15
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "UmRole编辑", description = "编辑参数")
public class UmRoleEditParam extends ApiParam {


    @ApiModelProperty(value = "角色编号")
    private String roleCode;

    @ApiModelProperty(value = "角色名称")
    private String roleName;

    @ApiModelProperty(value = "")
    private Long id;

    @ApiModelProperty(value = "备注")
    private String remark;
}
