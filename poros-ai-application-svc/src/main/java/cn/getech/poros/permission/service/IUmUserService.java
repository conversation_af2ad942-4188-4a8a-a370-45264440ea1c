package cn.getech.poros.permission.service;

import cn.getech.poros.permission.entity.UmUser;
import cn.getech.poros.framework.common.service.IBaseService;
import cn.getech.poros.permission.dto.UmUserQueryParam;
import cn.getech.poros.permission.dto.UmUserAddParam;
import cn.getech.poros.permission.dto.UmUserEditParam;
import cn.getech.poros.permission.dto.UmUserDto;
import cn.getech.poros.framework.common.api.PageResult;

import java.util.List;

/**
 * <pre>
 *  服务类
 * </pre>
 *
 * <AUTHOR>
 * @since 2022-11-04
 */
public interface IUmUserService extends IBaseService<UmUser> {

        /**
         * 分页查询，返回Dto
         *
         * @param umUserQueryParam
         * @return
         */
        PageResult<UmUserDto> pageDto(UmUserQueryParam umUserQueryParam);

        /**
         * 保存
         * @param umUserAddParam
         * @return
         */
        boolean saveByParam(UmUserAddParam umUserAddParam);

        /**
         * 根据id查询，转dto
         * @param id
         * @return
         */
        UmUserDto getDtoById(Long id);

        /**
         * 批量保存
         * @param rows
         */
        boolean saveDtoBatch(List<UmUserDto> rows);

        /**
         * 更新
         * @param umUserEditParam
         */
        boolean updateByParam(UmUserEditParam umUserEditParam);
}