package cn.getech.poros.permission.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import cn.getech.poros.framework.common.bean.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import cn.getech.poros.framework.common.annotation.Excel;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-15
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("um_menu")
public class UmMenu extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * 父级id
     */
    @TableField("parent_menu_id")
    private Integer parentMenuId;

    /**
     * 排序
     */
    @TableField("sequence")
    private Integer sequence;

    /**
     * 菜单编号
     */
    @TableField("menu_code")
    private String menuCode;

    /**
     * 菜单名称
     */
    @TableField("menu_name")
    private String menuName;

    /**
     * 路由地址
     */
    @TableField("menu_url")
    private String menuUrl;

    /**
     * 菜单icon
     */
    @TableField("icon")
    private String icon;

    /**
     * 权限标识
     */
    @TableField("mask")
    private String mask;

    /**
     * 隐藏标识：0 false-不隐藏，1 true-隐藏
     */
    @TableField("hidden")
    private String hidden;

    /**
     * 资源类型（菜单/按钮等）0：菜单 1：按钮 
     */
    @TableField("resource_type")
    private String resourceType;

    /**
     * 组件路径
     */
    @TableField("component_path")
    private String componentPath;


}
