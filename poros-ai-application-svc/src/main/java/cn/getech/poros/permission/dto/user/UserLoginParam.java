package cn.getech.poros.permission.dto.user;

import cn.getech.poros.framework.common.param.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <pre>
 *  分页查询参数对象
 * </pre>
 *
 * <AUTHOR>
 * @date 2022-06-08
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "UserLoginParam", description = "登录入参")
public class UserLoginParam extends PageParam {

    @ApiModelProperty(value = "用户名")
    private String userName;

    @ApiModelProperty(value = "密码")
    private String userPassword;

}
