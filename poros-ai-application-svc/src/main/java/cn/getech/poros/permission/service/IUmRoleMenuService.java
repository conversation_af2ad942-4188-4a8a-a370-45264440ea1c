package cn.getech.poros.permission.service;

import cn.getech.poros.permission.entity.UmRoleMenu;
import cn.getech.poros.framework.common.service.IBaseService;
import cn.getech.poros.permission.dto.UmRoleMenuQueryParam;
import cn.getech.poros.permission.dto.UmRoleMenuAddParam;
import cn.getech.poros.permission.dto.UmRoleMenuEditParam;
import cn.getech.poros.permission.dto.UmRoleMenuDto;
import cn.getech.poros.framework.common.api.PageResult;

import java.util.List;

/**
 * <pre>
 *  服务类
 * </pre>
 *
 * <AUTHOR>
 * @since 2022-08-15
 */
public interface IUmRoleMenuService extends IBaseService<UmRoleMenu> {

        /**
         * 分页查询，返回Dto
         *
         * @param umRoleMenuQueryParam
         * @return
         */
        PageResult<UmRoleMenuDto> pageDto(UmRoleMenuQueryParam umRoleMenuQueryParam);

        /**
         * 保存
         * @param umRoleMenuAddParam
         * @return
         */
        boolean saveByParam(UmRoleMenuAddParam umRoleMenuAddParam);

        /**
         * 根据id查询，转dto
         * @param id
         * @return
         */
        UmRoleMenuDto getDtoById(Long id);

        /**
         * 批量保存
         * @param rows
         */
        boolean saveDtoBatch(List<UmRoleMenuDto> rows);

        /**
         * 更新
         * @param umRoleMenuEditParam
         */
        boolean updateByParam(UmRoleMenuEditParam umRoleMenuEditParam);
}