package cn.getech.poros.permission.dto.user;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;


/**
 * <pre>
 *  返回数据模型
 * </pre>
 *
 * <AUTHOR>
 * @date 2022-06-07
 */
@Data
@ApiModel(value = "UserDetailsDto", description = "用户详情")
public class UserDetailsDto implements Serializable {

    @ApiModelProperty(value = "用户ID")
    private Long id;

    @ApiModelProperty(value = "角色ID")
    private String roleId;

    @ApiModelProperty(value = "用户名")
    private String userName;

    @ApiModelProperty(value = "卡号")
    private String idCardNo;

    @ApiModelProperty(value = "等级")
    private Integer level;

    @ApiModelProperty(value = "用户姓名")
    private String userAccount;

    @ApiModelProperty(value = "菜单列表")
    private List<MenuDto> menus;

    @ApiModelProperty(value = "按钮标识集合")
    private List<String> buttonMarks;


}