package cn.getech.poros.permission.dto;

import cn.getech.poros.framework.common.bean.BaseEntity;
import cn.getech.poros.permission.entity.UmUserRole;
import org.mapstruct.Mapper;
import cn.getech.poros.framework.common.api.PageResult;
import java.util.List;

/**
 * <pre>
 *  参数实体映射
 * </pre>
 *
 * <AUTHOR>
 * @date 2022-08-15
 */
@Mapper(componentModel = "spring",imports = {BaseEntity.class})
public interface  UmUserRoleParamMapper{

    /**
     * 新增参数转换为实体
     *
     * @param umUserRoleAddParam
     * @return
     */
    UmUserRole addParam2Entity(UmUserRoleAddParam umUserRoleAddParam);

    /**
     * 编辑参数转换为实体
     * @param umUserRoleEditParam
     * @return
     */
    UmUserRole editParam2Entity(UmUserRoleEditParam umUserRoleEditParam);

    /**
     * 实体转换为Dto
     * @param umUserRole
     * @return
     */
    UmUserRoleDto entity2Dto(UmUserRole umUserRole);

    /**
     * 分页实体转DTO
     * @param page
     * @return
     */
    PageResult<UmUserRoleDto> pageEntity2Dto(PageResult<UmUserRole> page);


    /**
     * dto集合转entity集合
     * @param rows
     * @return
     */
    List<UmUserRole> dtoList2Entity(List<UmUserRoleDto> rows);

}
