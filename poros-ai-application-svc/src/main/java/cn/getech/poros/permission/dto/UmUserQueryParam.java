package cn.getech.poros.permission.dto;

import cn.getech.poros.framework.common.param.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.Date;

/**
 * <pre>
 *  分页查询参数对象
 * </pre>
 *
 * <AUTHOR>
 * @date 2022-11-04
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "UmUser查询", description = "查询参数")
public class UmUserQueryParam extends PageParam {

    @ApiModelProperty(value = "待定")
    private String userCode;

    @ApiModelProperty(value = "用户名")
    private String userName;

    @ApiModelProperty(value = "卡号")
    private String idCardNo;

    @ApiModelProperty(value = "姓名")
    private String userAccount;

    @ApiModelProperty(value = "密码")
    private String userPassword;

    @ApiModelProperty(value = "等级")
    private Integer level;

    @ApiModelProperty(value = "")
    private Long id;

    @ApiModelProperty(value = "创建时间")
    private String[] createTimeParam;
    @ApiModelProperty(value = "更新时间")
    private String[] updateTimeParam;
    @ApiModelProperty(value = "创建人")
    private String createBy;

    @ApiModelProperty(value = "更新人")
    private String updateBy;

}
