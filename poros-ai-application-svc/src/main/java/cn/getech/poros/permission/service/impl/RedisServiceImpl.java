package cn.getech.poros.permission.service.impl;

import cn.getech.poros.permission.service.RedisService;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @title: RedisServiceImpl
 * @projectName muscle-parent
 * @description: TODO redis操作Service的实现类
 * @date 2020/4/916:47
 */
@Log4j2
@Service
public class RedisServiceImpl implements RedisService {

    @Autowired
    private StringRedisTemplate redisTemplate;

    public void setRedisTemplate(StringRedisTemplate redisTemplate) {
        this.redisTemplate = redisTemplate;
    }

    public StringRedisTemplate getRedisTemplate() {
        return this.redisTemplate;
    }

    @Override
    public void set(String key, String value, long time) {
        redisTemplate.opsForValue().set(key, value, time, TimeUnit.SECONDS);
    }


    @Override
    public void set(String key, String value) {
        redisTemplate.opsForValue().set(key, value);
    }

    @Override
    public Object get(String key) {
        return redisTemplate.opsForValue().get(key);
    }

    @Override
    public String getStr(String key) {
        return redisTemplate.opsForValue().get(key);
    }

    @Override
    public Object getVague(String key) {
        Set keys = redisTemplate.keys(key + "*");
        for (Object o : keys) {
            key = (String) o;
            break;
        }
        Object o = redisTemplate.opsForValue().get(key);
        return o;
    }

    @Override
    public boolean expire(String key, long expire) {
        return redisTemplate.expire(key, expire, TimeUnit.SECONDS);
    }

    @Override
    public void remove(String key) {
        redisTemplate.delete(key);
    }

    @Override
    public void removeVague(String key) {
        Set keys = redisTemplate.keys(key + "*");
        for (Object o : keys) {
            key = (String) o;
            remove(key);
        }

    }

    @Override
    public Boolean del(String key) {
        return redisTemplate.delete(key);
    }

    @Override
    public Long del(List<String> keys) {
        return redisTemplate.delete(keys);
    }

    @Override
    public Long increment(String key, long delta) {
        return redisTemplate.opsForValue().increment(key,delta);
    }

    @Override
    public boolean isKdy(String key) {
        return redisTemplate.hasKey(key);
    }
}
