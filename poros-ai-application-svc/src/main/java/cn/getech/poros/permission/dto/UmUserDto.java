package cn.getech.poros.permission.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import cn.getech.poros.framework.common.annotation.Excel;
import java.util.Date;


/**
 * <pre>
 *  返回数据模型
 * </pre>
 *
 * <AUTHOR>
 * @date 2022-11-04
 */
@Data
@ApiModel(value = "UmUserDto", description = "返回数据模型")
public class UmUserDto{

    @ApiModelProperty(value = "待定")
    @Excel(name="待定",cellType = Excel.ColumnType.STRING )
    private String userCode;

    @ApiModelProperty(value = "用户名")
    @Excel(name="用户名",cellType = Excel.ColumnType.STRING )
    private String userName;

    @ApiModelProperty(value = "卡号")
    @Excel(name="卡号",cellType = Excel.ColumnType.STRING )
    private String idCardNo;

    @ApiModelProperty(value = "姓名")
    @Excel(name="姓名",cellType = Excel.ColumnType.STRING )
    private String userAccount;

    @ApiModelProperty(value = "密码")
    @Excel(name="密码",cellType = Excel.ColumnType.STRING )
    private String userPassword;

    @ApiModelProperty(value = "等级")
    @Excel(name="等级",cellType = Excel.ColumnType.STRING )
    private Integer level;

    @ApiModelProperty(value = "")
    @Excel(name="",cellType = Excel.ColumnType.STRING)
    private Long id;

    @ApiModelProperty(value = "创建时间")
    @Excel(name="创建时间",cellType = Excel.ColumnType.STRING ,dateFormat="yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @ApiModelProperty(value = "更新时间")
    @Excel(name="更新时间",cellType = Excel.ColumnType.STRING ,dateFormat="yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    @ApiModelProperty(value = "创建人")
    @Excel(name="创建人",cellType = Excel.ColumnType.STRING )
    private String createBy;

    @ApiModelProperty(value = "更新人")
    @Excel(name="更新人",cellType = Excel.ColumnType.STRING )
    private String updateBy;

}