package cn.getech.poros.permission.dto.user;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;


/**
 * <pre>
 *  返回数据模型
 * </pre>
 *
 * <AUTHOR>
 * @date 2022-06-07
 */
@Data
@ApiModel(value = "MenuDto", description = "菜单返回数据模型")
public class MenuDto implements Serializable {

    @ApiModelProperty(value = "选中状态")
    private Boolean checked;
    @ApiModelProperty(value = "父级id")
    private Long parentMenuId;
    @ApiModelProperty(value = "排序")
    private Integer sequence;
    @ApiModelProperty(value = "菜单编号")
    private String menuCode;
    @ApiModelProperty(value = "菜单名称")
    private String menuName;
    @ApiModelProperty(value = "路由地址")
    private String menuUrl;
    @ApiModelProperty(value = "菜单icon")
    private String icon;
    @ApiModelProperty(value = "权限标识")
    private String mask;
    @ApiModelProperty(value = "隐藏标识：0 false-不隐藏，1 true-隐藏")
    private String hidden;
    @ApiModelProperty(value = "资源类型（菜单/按钮等）0：菜单 1：按钮 ")
    private String resourceType;
    @ApiModelProperty(value = "组件路径")
    private String componentPath;
    @ApiModelProperty(value = "")
    private Long id;
    @ApiModelProperty(value = "备注")
    private String remark;
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    @ApiModelProperty(value = "子菜单")
    private List<MenuDto> children;
}