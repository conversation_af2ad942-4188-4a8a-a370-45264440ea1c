package cn.getech.poros.permission.service;


import cn.getech.poros.framework.common.api.PageResult;
import cn.getech.poros.permission.dto.user.*;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

public interface UserService {

    /**
     * 根据用户名获取后台管理员
     */
    UserDto getUsername(String username)throws Exception;

    /**
     * 用户注册
     */
    @Transactional
    void register(UserAddParam param) throws Exception;

    /**
     * 登录
     *
     * @return
     */
    String login(UserLoginParam param) throws Exception;


    void logout()throws Exception;

    /**
     * 用户列表
     * @param
     * @throws Exception
     */
    PageResult<UserListDto> userList(UserListParam param, int pageNum, int pageSize)throws Exception;

    /**
     * 编辑用户
     * @param param
     * @throws Exception
     */
    void editUser(UserEditParam param)throws Exception;
    /**
     * 编辑用户
     * @param param
     * @throws Exception
     */
    void roleWithUsersAssociation(RoleWithUsersAssociationParam param)throws Exception;
    /**
     * 修改用户密码
     * @param param
     * @throws Exception
     */
    void updateUserPassword(UserEditPasswordParam param)throws Exception;

    /**
     * 获取用户信息
     */
    UserDetails loadUserByUsername(String userName)throws Exception;

    /**
     *  更新用户关联角色
     * @param userId
     * @param roleId
     */
    void updateUserRole(Long userId, Long roleId);

    List<MenuDto> getResourceList(Long roleId);

    UserDetailsDto getUserInfo()throws Exception;

    void delUser(Long[] userIds);

    PageResult<RoleListDto> roleList(RoleListParam param)throws Exception;

    void saveRole(RoleAddParam param)throws Exception;

    void updateRole(RoleEditParam param)throws Exception;

    void delRole(Long[] userIds);

    List<MenuDto> menuList(String menuName);

    void addMenu(MenuAddParam param)throws Exception;

    void updateMenu(MenuEditParam param)throws Exception;

    void delMenu(Long[] menuId)throws Exception;

    List<MenuDto> roleMenus(Long roleId);

    void updateRoleMenus(MenuParam param);
 }
