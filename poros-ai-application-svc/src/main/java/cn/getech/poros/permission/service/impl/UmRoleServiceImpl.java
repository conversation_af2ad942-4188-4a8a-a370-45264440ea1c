package cn.getech.poros.permission.service.impl;

import cn.getech.poros.permission.entity.UmRole;
import cn.getech.poros.permission.mapper.UmRoleMapper;
import cn.getech.poros.permission.service.IUmRoleService;
import cn.getech.poros.framework.common.service.impl.BaseServiceImpl;
import cn.getech.poros.permission.dto.UmRoleQueryParam;
import cn.getech.poros.permission.dto.UmRoleAddParam;
import cn.getech.poros.permission.dto.UmRoleEditParam;
import cn.getech.poros.permission.dto.UmRoleParamMapper;
import cn.getech.poros.permission.dto.UmRoleDto;
import cn.getech.poros.framework.common.bean.BaseEntity;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import cn.getech.poros.framework.common.api.PageResult;
import cn.getech.poros.framework.common.api.Assert;
import cn.getech.poros.framework.common.api.ResultCode;
import java.util.Optional;
import java.util.List;


/**
 * <pre>
 *  服务实现类
 * </pre>
 *
 * <AUTHOR>
 * @since 2022-08-15
 */
@Slf4j
@Service
public class UmRoleServiceImpl extends BaseServiceImpl<UmRoleMapper, UmRole> implements IUmRoleService {

    @Autowired
    private UmRoleParamMapper umRoleParamMapper;

    @Override
    public PageResult<UmRoleDto> pageDto(UmRoleQueryParam umRoleQueryParam) {
        Wrapper<UmRole> wrapper = getPageSearchWrapper(umRoleQueryParam);
        PageResult<UmRoleDto> result = umRoleParamMapper.pageEntity2Dto(page(umRoleQueryParam, wrapper));

        return Optional.ofNullable(result).orElse(new PageResult<>());
    }

    @SuppressWarnings("unchecked")
    @Override
    public boolean saveByParam(UmRoleAddParam umRoleAddParam) {
        UmRole umRole = umRoleParamMapper.addParam2Entity(umRoleAddParam);
        Assert.notNull(ResultCode.PARAM_VALID_ERROR,umRole);
        return save(umRole);
    }

    @SuppressWarnings("unchecked")
    @Override
    public boolean updateByParam(UmRoleEditParam umRoleEditParam) {
        UmRole umRole = umRoleParamMapper.editParam2Entity(umRoleEditParam);
        Assert.notNull(ResultCode.PARAM_VALID_ERROR,umRole);
        return updateById(umRole);
    }


    @Override
    public UmRoleDto getDtoById(Long id) {
        return umRoleParamMapper.entity2Dto((UmRole) this.getById(id));
    }

    @SuppressWarnings("unchecked")
    @Override
    public boolean saveDtoBatch(List<UmRoleDto> rows) {
        return saveBatch(umRoleParamMapper.dtoList2Entity(rows));
    }

    private Wrapper<UmRole> getPageSearchWrapper(UmRoleQueryParam umRoleQueryParam) {
        LambdaQueryWrapper<UmRole> wrapper = Wrappers.<UmRole>lambdaQuery();
        if(BaseEntity.class.isAssignableFrom(UmRole.class)){
            wrapper.orderByDesc(UmRole::getUpdateTime,UmRole::getCreateTime);
        }
        return wrapper;
    }
}
