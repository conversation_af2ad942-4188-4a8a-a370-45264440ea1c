package cn.getech.poros.permission.service.impl;

import cn.getech.poros.permission.entity.UmUser;
import cn.getech.poros.permission.mapper.UmUserMapper;
import cn.getech.poros.permission.service.IUmUserService;
import cn.getech.poros.framework.common.service.impl.BaseServiceImpl;
import cn.getech.poros.permission.dto.UmUserQueryParam;
import cn.getech.poros.permission.dto.UmUserAddParam;
import cn.getech.poros.permission.dto.UmUserEditParam;
import cn.getech.poros.permission.dto.UmUserParamMapper;
import cn.getech.poros.permission.dto.UmUserDto;
import cn.getech.poros.framework.common.bean.BaseEntity;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import cn.getech.poros.framework.common.api.PageResult;
import cn.getech.poros.framework.common.api.Assert;
import cn.getech.poros.framework.common.api.ResultCode;
import java.util.Optional;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import cn.hutool.core.util.ArrayUtil;



/**
 * <pre>
 *  服务实现类
 * </pre>
 *
 * <AUTHOR>
 * @since 2022-11-04
 */
@Slf4j
@Service
public class UmUserServiceImpl extends BaseServiceImpl<UmUserMapper, UmUser> implements IUmUserService {

    @Autowired
    private UmUserParamMapper umUserParamMapper;

    @Override
    public PageResult<UmUserDto> pageDto(UmUserQueryParam umUserQueryParam) {
        Wrapper<UmUser> wrapper = getPageSearchWrapper(umUserQueryParam);
        PageResult<UmUserDto> result = umUserParamMapper.pageEntity2Dto(page(umUserQueryParam, wrapper));

        return Optional.ofNullable(result).orElse(new PageResult<>());
    }

    @SuppressWarnings("unchecked")
    @Override
    public boolean saveByParam(UmUserAddParam umUserAddParam) {
        UmUser umUser = umUserParamMapper.addParam2Entity(umUserAddParam);
        Assert.notNull(ResultCode.PARAM_VALID_ERROR,umUser);
        return save(umUser);
    }

    @SuppressWarnings("unchecked")
    @Override
    public boolean updateByParam(UmUserEditParam umUserEditParam) {
        UmUser umUser = umUserParamMapper.editParam2Entity(umUserEditParam);
        Assert.notNull(ResultCode.PARAM_VALID_ERROR,umUser);
        return updateById(umUser);
    }


    @Override
    public UmUserDto getDtoById(Long id) {
        return umUserParamMapper.entity2Dto((UmUser) this.getById(id));
    }

    @SuppressWarnings("unchecked")
    @Override
    public boolean saveDtoBatch(List<UmUserDto> rows) {
        return saveBatch(umUserParamMapper.dtoList2Entity(rows));
    }

    private Wrapper<UmUser> getPageSearchWrapper(UmUserQueryParam umUserQueryParam) {
        LambdaQueryWrapper<UmUser> wrapper = Wrappers.<UmUser>lambdaQuery();
        wrapper.like(StringUtils.isNotBlank(umUserQueryParam.getUserCode()),
                     UmUser::getUserCode, umUserQueryParam.getUserCode());
        wrapper.like(StringUtils.isNotBlank(umUserQueryParam.getUserName()),
                     UmUser::getUserName, umUserQueryParam.getUserName());
        wrapper.like(StringUtils.isNotBlank(umUserQueryParam.getIdCardNo()),
                     UmUser::getIdCardNo, umUserQueryParam.getIdCardNo());
        wrapper.like(StringUtils.isNotBlank(umUserQueryParam.getUserAccount()),
                     UmUser::getUserAccount, umUserQueryParam.getUserAccount());
        wrapper.like(StringUtils.isNotBlank(umUserQueryParam.getUserPassword()),
                     UmUser::getUserPassword, umUserQueryParam.getUserPassword());
        wrapper.like(null != umUserQueryParam.getLevel(),
                    UmUser::getLevel, umUserQueryParam.getLevel());
        wrapper.like(null != umUserQueryParam.getId(),
                    UmUser::getId, umUserQueryParam.getId());
        String[] createTimeParam = umUserQueryParam.getCreateTimeParam();
        if (ArrayUtil.length(createTimeParam) == 2){
            wrapper.between(UmUser::getCreateTime, createTimeParam[0], createTimeParam[1]);
        }
        String[] updateTimeParam = umUserQueryParam.getUpdateTimeParam();
        if (ArrayUtil.length(updateTimeParam) == 2){
            wrapper.between(UmUser::getUpdateTime, updateTimeParam[0], updateTimeParam[1]);
        }
        wrapper.like(StringUtils.isNotBlank(umUserQueryParam.getCreateBy()),
                     UmUser::getCreateBy, umUserQueryParam.getCreateBy());
        wrapper.like(StringUtils.isNotBlank(umUserQueryParam.getUpdateBy()),
                     UmUser::getUpdateBy, umUserQueryParam.getUpdateBy());
        if(BaseEntity.class.isAssignableFrom(UmUser.class)){
            wrapper.orderByDesc(UmUser::getUpdateTime,UmUser::getCreateTime);
        }
        return wrapper;
    }
}
