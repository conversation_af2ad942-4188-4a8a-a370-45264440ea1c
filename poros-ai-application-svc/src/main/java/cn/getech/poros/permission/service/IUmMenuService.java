package cn.getech.poros.permission.service;

import cn.getech.poros.permission.entity.UmMenu;
import cn.getech.poros.framework.common.service.IBaseService;
import cn.getech.poros.permission.dto.UmMenuQueryParam;
import cn.getech.poros.permission.dto.UmMenuAddParam;
import cn.getech.poros.permission.dto.UmMenuDto;
import cn.getech.poros.framework.common.api.PageResult;

import java.util.List;

/**
 * <pre>
 *  服务类
 * </pre>
 *
 * <AUTHOR>
 * @since 2022-08-15
 */
public interface IUmMenuService extends IBaseService<UmMenu> {

        /**
         * 分页查询，返回Dto
         *
         * @param umMenuQueryParam
         * @return
         */
        PageResult<UmMenuDto> pageDto(UmMenuQueryParam umMenuQueryParam);

        /**
         * 保存
         * @param umMenuAddParam
         * @return
         */
        boolean saveByParam(UmMenuAddParam umMenuAddParam);

        /**
         * 根据id查询，转dto
         * @param id
         * @return
         */
        UmMenuDto getDtoById(Long id);

        /**
         * 批量保存
         * @param rows
         */
        boolean saveDtoBatch(List<UmMenuDto> rows);


}