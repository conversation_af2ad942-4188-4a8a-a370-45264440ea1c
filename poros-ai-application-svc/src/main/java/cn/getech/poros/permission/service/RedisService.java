package cn.getech.poros.permission.service;

import java.util.List;

/**
 * <AUTHOR>
 * @title: RedisService
 * @projectName muscle-parent
 * @description: TODO redis操作Service,对象和数组都以json形式进行存储
 * @date 2020/4/916:47
 */
public interface RedisService {


    /**
     * 保存属性
     */
    void set(String key, String value, long time);

    /**
     * 存储数据
     */
    void set(String key, String value);

    /**
     * 获取数据
     */
    Object get(String key);
    /**
     * 获取数据
     */
    String getStr(String key);


    /**
     * 获取数据
     */
    Object getVague(String key);

    /**
     * 设置超期时间
     */
    boolean expire(String key, long expire);

    /**
     * 删除数据
     */
    void remove(String key);


    /**
     * 删除数据
     */
    void removeVague(String key);


    /**
     * 删除属性
     */
    Boolean del(String key);

    /**
     * 批量删除属性
     */
    Long del(List<String> keys);

    /**
     * 自增操作
     * @param delta 自增步长
     */
    Long increment(String key, long delta);


    boolean isKdy(String key);

}
