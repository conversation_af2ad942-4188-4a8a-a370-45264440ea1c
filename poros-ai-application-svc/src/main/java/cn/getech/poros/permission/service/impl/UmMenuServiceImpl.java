package cn.getech.poros.permission.service.impl;

import cn.getech.poros.permission.entity.UmMenu;
import cn.getech.poros.permission.mapper.UmMenuMapper;
import cn.getech.poros.permission.service.IUmMenuService;
import cn.getech.poros.framework.common.service.impl.BaseServiceImpl;
import cn.getech.poros.permission.dto.UmMenuQueryParam;
import cn.getech.poros.permission.dto.UmMenuAddParam;
import cn.getech.poros.permission.dto.UmMenuParamMapper;
import cn.getech.poros.permission.dto.UmMenuDto;
import cn.getech.poros.framework.common.bean.BaseEntity;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import cn.getech.poros.framework.common.api.PageResult;
import cn.getech.poros.framework.common.api.Assert;
import cn.getech.poros.framework.common.api.ResultCode;
import java.util.Optional;
import java.util.List;


/**
 * <pre>
 *  服务实现类
 * </pre>
 *
 * <AUTHOR>
 * @since 2022-08-15
 */
@Slf4j
@Service
public class UmMenuServiceImpl extends BaseServiceImpl<UmMenuMapper, UmMenu> implements IUmMenuService {

    @Autowired
    private UmMenuParamMapper umMenuParamMapper;

    @Override
    public PageResult<UmMenuDto> pageDto(UmMenuQueryParam umMenuQueryParam) {
        Wrapper<UmMenu> wrapper = getPageSearchWrapper(umMenuQueryParam);
        PageResult<UmMenuDto> result = umMenuParamMapper.pageEntity2Dto(page(umMenuQueryParam, wrapper));

        return Optional.ofNullable(result).orElse(new PageResult<>());
    }

    @SuppressWarnings("unchecked")
    @Override
    public boolean saveByParam(UmMenuAddParam umMenuAddParam) {
        UmMenu umMenu = umMenuParamMapper.addParam2Entity(umMenuAddParam);
        Assert.notNull(ResultCode.PARAM_VALID_ERROR,umMenu);
        return save(umMenu);
    }




    @Override
    public UmMenuDto getDtoById(Long id) {
        return umMenuParamMapper.entity2Dto((UmMenu) this.getById(id));
    }

    @SuppressWarnings("unchecked")
    @Override
    public boolean saveDtoBatch(List<UmMenuDto> rows) {
        return saveBatch(umMenuParamMapper.dtoList2Entity(rows));
    }

    private Wrapper<UmMenu> getPageSearchWrapper(UmMenuQueryParam umMenuQueryParam) {
        LambdaQueryWrapper<UmMenu> wrapper = Wrappers.<UmMenu>lambdaQuery();
        if(BaseEntity.class.isAssignableFrom(UmMenu.class)){
            wrapper.orderByDesc(UmMenu::getUpdateTime,UmMenu::getCreateTime);
        }
        return wrapper;
    }
}
