package cn.getech.poros.permission.service;

import cn.getech.poros.permission.entity.UmRole;
import cn.getech.poros.framework.common.service.IBaseService;
import cn.getech.poros.permission.dto.UmRoleQueryParam;
import cn.getech.poros.permission.dto.UmRoleAddParam;
import cn.getech.poros.permission.dto.UmRoleEditParam;
import cn.getech.poros.permission.dto.UmRoleDto;
import cn.getech.poros.framework.common.api.PageResult;

import java.util.List;

/**
 * <pre>
 *  服务类
 * </pre>
 *
 * <AUTHOR>
 * @since 2022-08-15
 */
public interface IUmRoleService extends IBaseService<UmRole> {

        /**
         * 分页查询，返回Dto
         *
         * @param umRoleQueryParam
         * @return
         */
        PageResult<UmRoleDto> pageDto(UmRoleQueryParam umRoleQueryParam);

        /**
         * 保存
         * @param umRoleAddParam
         * @return
         */
        boolean saveByParam(UmRoleAddParam umRoleAddParam);

        /**
         * 根据id查询，转dto
         * @param id
         * @return
         */
        UmRoleDto getDtoById(Long id);

        /**
         * 批量保存
         * @param rows
         */
        boolean saveDtoBatch(List<UmRoleDto> rows);

        /**
         * 更新
         * @param umRoleEditParam
         */
        boolean updateByParam(UmRoleEditParam umRoleEditParam);
}