package cn.getech.poros.permission.mapper;

import cn.getech.poros.permission.dto.user.*;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-08
 */
public interface UserMapper {


    List<UserListDto> userList(UserListParam param);

    List<RoleListDto> roleList(RoleListParam param);

    List<String> getUserNameList(RoleWithUsersAssociationParam param);

    List<MenuDto> menuListByRoleId(@Param("roleId") Long roleId);

    List<MenuDto> menuList(@Param("menuName") String menuName);
}
