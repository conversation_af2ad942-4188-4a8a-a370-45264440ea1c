package cn.getech.poros.permission.dto;

import cn.getech.poros.framework.common.bean.BaseEntity;
import cn.getech.poros.permission.entity.UmMenu;
import org.mapstruct.Mapper;
import cn.getech.poros.framework.common.api.PageResult;
import java.util.List;

/**
 * <pre>
 *  参数实体映射
 * </pre>
 *
 * <AUTHOR>
 * @date 2022-08-15
 */
@Mapper(componentModel = "spring",imports = {BaseEntity.class})
public interface  UmMenuParamMapper{

    /**
     * 新增参数转换为实体
     *
     * @param umMenuAddParam
     * @return
     */
    UmMenu addParam2Entity(UmMenuAddParam umMenuAddParam);



    /**
     * 实体转换为Dto
     * @param umMenu
     * @return
     */
    UmMenuDto entity2Dto(UmMenu umMenu);

    /**
     * 分页实体转DTO
     * @param page
     * @return
     */
    PageResult<UmMenuDto> pageEntity2Dto(PageResult<UmMenu> page);


    /**
     * dto集合转entity集合
     * @param rows
     * @return
     */
    List<UmMenu> dtoList2Entity(List<UmMenuDto> rows);

}
