package cn.getech.poros.permission.dto.user;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;


/**
 * <pre>
 *  返回数据模型
 * </pre>
 *
 * <AUTHOR>
 * @date 2022-06-07
 */
@Data
@ApiModel(value = "MenuParam", description = "分配资源入参")
public class MenuParam implements Serializable {


    @ApiModelProperty(value = "roleId")
    private Long roleId;

    @ApiModelProperty(value = "菜单资源列表")
    List<MenuDto> menus;
}