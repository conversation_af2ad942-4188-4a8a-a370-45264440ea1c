package cn.getech.poros.permission.dto;

import cn.getech.poros.framework.common.bean.BaseEntity;
import cn.getech.poros.permission.entity.UmRole;
import org.mapstruct.Mapper;
import cn.getech.poros.framework.common.api.PageResult;
import java.util.List;

/**
 * <pre>
 *  参数实体映射
 * </pre>
 *
 * <AUTHOR>
 * @date 2022-08-15
 */
@Mapper(componentModel = "spring",imports = {BaseEntity.class})
public interface  UmRoleParamMapper{

    /**
     * 新增参数转换为实体
     *
     * @param umRoleAddParam
     * @return
     */
    UmRole addParam2Entity(UmRoleAddParam umRoleAddParam);

    /**
     * 编辑参数转换为实体
     * @param umRoleEditParam
     * @return
     */
    UmRole editParam2Entity(UmRoleEditParam umRoleEditParam);

    /**
     * 实体转换为Dto
     * @param umRole
     * @return
     */
    UmRoleDto entity2Dto(UmRole umRole);

    /**
     * 分页实体转DTO
     * @param page
     * @return
     */
    PageResult<UmRoleDto> pageEntity2Dto(PageResult<UmRole> page);


    /**
     * dto集合转entity集合
     * @param rows
     * @return
     */
    List<UmRole> dtoList2Entity(List<UmRoleDto> rows);

}
