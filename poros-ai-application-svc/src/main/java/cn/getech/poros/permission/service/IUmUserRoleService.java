package cn.getech.poros.permission.service;

import cn.getech.poros.permission.entity.UmUserRole;
import cn.getech.poros.framework.common.service.IBaseService;
import cn.getech.poros.permission.dto.UmUserRoleQueryParam;
import cn.getech.poros.permission.dto.UmUserRoleAddParam;
import cn.getech.poros.permission.dto.UmUserRoleEditParam;
import cn.getech.poros.permission.dto.UmUserRoleDto;
import cn.getech.poros.framework.common.api.PageResult;
import java.util.List;

/**
 * <pre>
 *  服务类
 * </pre>
 *
 * <AUTHOR>
 * @since 2022-08-15
 */
public interface IUmUserRoleService extends IBaseService<UmUserRole> {

        /**
         * 分页查询，返回Dto
         *
         * @param umUserRoleQueryParam
         * @return
         */
        PageResult<UmUserRoleDto> pageDto(UmUserRoleQueryParam umUserRoleQueryParam);

        /**
         * 保存
         * @param umUserRoleAddParam
         * @return
         */
        boolean saveByParam(UmUserRoleAddParam umUserRoleAddParam);

        /**
         * 根据id查询，转dto
         * @param id
         * @return
         */
        UmUserRoleDto getDtoById(Long id);

        /**
         * 批量保存
         * @param rows
         */
        boolean saveDtoBatch(List<UmUserRoleDto> rows);

        /**
         * 更新
         * @param umUserRoleEditParam
         */
        boolean updateByParam(UmUserRoleEditParam umUserRoleEditParam);
}