package cn.getech.poros.permission.dto.user;

import cn.getech.poros.framework.common.param.ApiParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <pre>
 *  新增参数
 * </pre>
 *
 * <AUTHOR>
 * @date 2022-06-08
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "UseEditParam", description = "编辑用户入参")
public class UserEditParam extends ApiParam {


    @ApiModelProperty(value = "主键")
    private Long id;

    @ApiModelProperty(value = "用户名")
    private String userName;

    @ApiModelProperty(value = "卡号")
    private String idCardNo;

    @ApiModelProperty(value = "等级")
    private Integer level;

    @ApiModelProperty(value = "员工姓名")
    private String userAccount;

    @ApiModelProperty(value = "旧密码")
    private String userPasswordOld;

    @ApiModelProperty(value = "新密码")
    private String userPasswordNew;

    @ApiModelProperty(value = "角色id")
    private Long roleId;

}