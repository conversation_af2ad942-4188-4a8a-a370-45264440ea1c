package cn.getech.poros.permission.service.impl;

import cn.getech.poros.framework.common.api.PageResult;
import cn.getech.poros.permission.bo.AdminUserDetails;
import cn.getech.poros.permission.dto.UmRoleMenuDto;
import cn.getech.poros.permission.dto.UmUserRoleDto;
import cn.getech.poros.permission.dto.user.*;
import cn.getech.poros.permission.entity.*;
import cn.getech.poros.permission.enums.MenuTypeEnum;
import cn.getech.poros.permission.mapper.UserMapper;
import cn.getech.poros.permission.service.*;
import cn.getech.poros.utils.JwtTokenUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class UserServiceImpl implements UserService {

    @Resource
    private JwtTokenUtil jwtTokenUtil;

    @Resource
    private PasswordEncoder passwordEncoder;

    @Resource
    private UserMapper userMapper;

    @Resource
    private IUmUserRoleService UmUserRoleService;

    @Resource
    private UserCacheService userCacheService;

    @Resource
    private IUmUserService userService;

    @Resource
    private IUmRoleService roleService;

    @Resource
    private IUmRoleMenuService roleMenuService;

    @Resource
    private IUmMenuService menuService;


    @Override
    public UserDto getUsername(String username) throws Exception {
        UserDto userDto = userCacheService.getUser(username);
        if (userDto != null) return userDto;
        LambdaQueryWrapper<UmUser> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(UmUser::getUserName, username);
        List<UmUser> UmUsers = userService.list(Wrappers.<UmUser>lambdaQuery()
                .eq(UmUser::getUserName, username)
        );
        if (UmUsers != null && UmUsers.size() > 0) {
            UmUser UmUser = UmUsers.get(0);
            LambdaQueryWrapper<UmUserRole> lambdaQueryWrapper1 = new LambdaQueryWrapper<>();
            lambdaQueryWrapper1.eq(UmUserRole::getUserId, UmUser.getId());
            List<UmUserRole> UmUserRoles = UmUserRoleService.list(lambdaQueryWrapper1);
            UserDto userDto1 = new UserDto();
            BeanUtils.copyProperties(UmUser, userDto1);
            if (UmUserRoles.size() > 0) {
                UmUserRole UmUserRole = UmUserRoles.get(0);
                userDto1.setRoleId(UmUserRole.getRoleId());
            }
            userCacheService.setUser(userDto1);
            return userDto1;
        }
        throw new Exception("用户不存在");
    }

    @Override
    public void register(UserAddParam param) throws Exception {

        LambdaQueryWrapper<UmUser> userLambdaQueryWrapper = new LambdaQueryWrapper<>();
        userLambdaQueryWrapper.eq(UmUser::getUserName, param.getUserName());
        Integer userCount = userService.count(userLambdaQueryWrapper);
        if (userCount > 0) {
            throw new Exception("用户已存在");
        }

        UmUser umUser = new UmUser();
        umUser.setUserAccount(param.getUserAccount());
        umUser.setIdCardNo(param.getIdCardNo());
        umUser.setLevel(param.getLevel());
        umUser.setUserName(param.getUserName());
        umUser.setUserPassword(passwordEncoder.encode(param.getUserPassword()));
        userService.save(umUser);
        updateUserRole(umUser.getId(), param.getRoleId());
    }

    @Override
    public String login(UserLoginParam param) throws Exception {

        String token;
        String userName = param.getUserName();
        UserDetails userDetails = loadUserByUsername(param.getUserName());
        if (!passwordEncoder.matches(param.getUserPassword(), userDetails.getPassword())) {
            throw new Exception("密码不正确");
        }
        UsernamePasswordAuthenticationToken authentication = new UsernamePasswordAuthenticationToken(userDetails, null, userDetails.getAuthorities());
        SecurityContextHolder.getContext().setAuthentication(authentication);
        token = jwtTokenUtil.generateToken(userDetails);
        userCacheService.setToken(userName, token);
        UserDto username = this.getUsername(userName);
        return token;
    }

    @Override
    public void logout()throws Exception {
        UsernamePasswordAuthenticationToken authentication = (UsernamePasswordAuthenticationToken) SecurityContextHolder.getContext().getAuthentication();
        UserDetails userDetails = (UserDetails) authentication.getPrincipal();

    }

    @Override
    public PageResult<UserListDto> userList(UserListParam param, int pageNum, int pageSize) throws Exception {
        PageHelper.startPage(pageNum, pageSize);
        List<UserListDto> userListDtos = userMapper.userList(param);
        PageInfo<UserListDto> pageInfo = new PageInfo<>(userListDtos);

        PageResult<UserListDto> result = new PageResult<>();
        result.setTotal(pageInfo.getTotal());
        result.setRecords(userListDtos);
        return result;
    }

    @Override
    @Transactional
    public void editUser(UserEditParam param) throws Exception {
        Long id = param.getId();
        String userName = param.getUserName();
        UmUser umUser = new UmUser();
        umUser.setId(id);
        umUser.setUserAccount(param.getUserAccount());
        umUser.setIdCardNo(param.getIdCardNo());
        umUser.setLevel(param.getLevel());
        userService.updateById(umUser);
        updateUserRole(id, param.getRoleId());
        userCacheService.delUser(userName);

    }

    @Override
    public void roleWithUsersAssociation(RoleWithUsersAssociationParam param) throws Exception {
        Long roleId = param.getRoleId();
        List<Long> userIds = param.getUserIds();

        List<String> userListDtos = userMapper.getUserNameList(param);
        userCacheService.delUsersListByRole(userListDtos);
        List<UmUserRoleDto> UmUserRoleDtos = new ArrayList<>();
        LambdaQueryWrapper<UmUserRole> roleLambdaQueryWrapper = new LambdaQueryWrapper<>();
        roleLambdaQueryWrapper.eq(UmUserRole::getRoleId, roleId);
        if (CollectionUtils.isNotEmpty(userIds)) {
            roleLambdaQueryWrapper.or().in(UmUserRole::getUserId, userIds);
        }
        UmUserRoleService.remove(roleLambdaQueryWrapper);
        userIds.forEach(v -> {
            UmUserRoleDto UmUserRole = new UmUserRoleDto();
            UmUserRole.setUserId(v);
            UmUserRole.setRoleId(roleId);
            UmUserRoleDtos.add(UmUserRole);
        });
        UmUserRoleService.saveDtoBatch(UmUserRoleDtos);


    }

    @Override
    public void updateUserPassword(UserEditPasswordParam param) throws Exception {
        Long id = param.getId();
        String userName = param.getUserName();
        UserDetails userDetails = loadUserByUsername(userName);
        if (!passwordEncoder.matches(param.getUserPasswordOld(), userDetails.getPassword())) {
            throw new Exception("原密码不正确");
        }
        UmUser UmUser = new UmUser();
        UmUser.setId(id);
        UmUser.setUserPassword(passwordEncoder.encode(param.getUserPasswordNew()));
        userService.updateById(UmUser);
        userCacheService.delUser(userName);
    }

    @Override
    public UserDetails loadUserByUsername(String userName) throws Exception {
        //获取用户信息
        UserDto userDto = getUsername(userName);
        return new AdminUserDetails(userDto);
    }

    public void updateUserRole(Long userId, Long roleId) {
        LambdaQueryWrapper<UmUserRole> removeLambda = new LambdaQueryWrapper<>();
        removeLambda.eq(UmUserRole::getUserId, userId);
        UmUserRoleService.remove(removeLambda);
        UmUserRole UmUserRole = new UmUserRole();
        UmUserRole.setUserId(userId);
        UmUserRole.setRoleId(roleId);
        UmUserRoleService.save(UmUserRole);
    }

    @Override
    public List<MenuDto> getResourceList(Long roleId) {
        List<MenuDto> resourceList = userCacheService.getResourceList(roleId);
        if (resourceList != null) return resourceList;

        List<MenuDto> menuDtos = userMapper.menuListByRoleId(roleId);
        if (menuDtos != null && menuDtos.size() > 0) {
            userCacheService.setResourceList(roleId, menuDtos);
            return menuDtos;
        }
        return new ArrayList<>();
    }

    @Override
    public UserDetailsDto getUserInfo() throws Exception {

        //获取用户信息
        UsernamePasswordAuthenticationToken authentication = (UsernamePasswordAuthenticationToken) SecurityContextHolder.getContext().getAuthentication();
        UserDetails userDetails = (UserDetails) authentication.getPrincipal();
        if (null == userDetails) {
            throw new Exception("获取用户信息失败");
        }
        String username1 = userDetails.getUsername();
        UserDetailsDto userDetailsDto = new UserDetailsDto();
        UserDto userDto = getUsername(username1);
        BeanUtils.copyProperties(userDto, userDetailsDto);
        String userName = userDto.getUserName();
        List<MenuDto> resourceList;
        //admin管理员
        if (userName.equals("admin")) {
            resourceList = userMapper.menuList(null);
        } else {
            Long roleId = userDto.getRoleId();
            resourceList = getResourceList(roleId);
        }
        List<MenuDto> menuDtoList = menuTree(resourceList);
        userDetailsDto.setMenus(menuDtoList);
        List<String> buttonMarks = resourceList.stream().filter(v -> MenuTypeEnum.BUTTON_TYPE.getRelType().equals(v.getResourceType())).map(MenuDto::getMenuCode).collect(Collectors.toList());
        userDetailsDto.setButtonMarks(buttonMarks);
        return userDetailsDto;
    }

    @Transactional
    @Override
    public void delUser(Long[] id) {
        List<Long> longs = Arrays.asList(id);
        List<UmUser> list = userService.listByIds(longs);
        List<String> userNames = list.stream().map(UmUser::getUserName).collect(Collectors.toList());
        userCacheService.delUsersListByRole(userNames);
        userService.removeByIds(id);
        for (Long aLong : id) {
            LambdaQueryWrapper<UmUserRole> lambdaQueryWrapper = new LambdaQueryWrapper<>();
            lambdaQueryWrapper.eq(UmUserRole::getUserId, aLong);
            UmUserRoleService.remove(lambdaQueryWrapper);
        }
    }

    @Override
    public PageResult<RoleListDto> roleList(RoleListParam param) throws Exception {
        PageHelper.startPage(param.getPageNo(),param.getLimit());
        List<RoleListDto> roleListDtos = userMapper.roleList(param);
        PageInfo<RoleListDto> pageInfo = new PageInfo<>(roleListDtos);
        PageResult<RoleListDto> result = new PageResult<>();
        result.setRecords(pageInfo.getList());
        result.setTotal(pageInfo.getTotal());
        return result;
    }

    @Override
    public void saveRole(RoleAddParam param) throws Exception {
        UmRole role = new UmRole();
        BeanUtils.copyProperties(param, role);
        roleService.save(role);
    }

    @Override
    public void updateRole(RoleEditParam param) throws Exception {
        UmRole role = new UmRole();
        BeanUtils.copyProperties(param, role);
        roleService.updateById(role);
    }

    @Transactional
    @Override
    public void delRole(Long[] roleIds) {
        for (Long roleId : roleIds) {
            delRoleCorrelation(roleId);
            LambdaQueryWrapper<UmRoleMenu> lambdaQueryWrapper = new LambdaQueryWrapper<>();
            lambdaQueryWrapper.eq(UmRoleMenu::getRoleId, roleId);
            roleMenuService.remove(lambdaQueryWrapper);
        }
        roleService.removeByIds(roleIds);
    }

    private void delRoleCorrelation(Long roleId) {
        UserListParam param = new UserListParam();
        param.setRoleId(roleId);
        List<UserListDto> userListDtos = userMapper.userList(param);
        List<String> collect = userListDtos.stream().map(v -> v.getUserName()).collect(Collectors.toList());
        userCacheService.delUsersListByRole(collect);
        UmUserRoleService.remove(Wrappers.<UmUserRole>lambdaQuery().eq(UmUserRole::getRoleId, roleId));
    }

    @Override
    public List<MenuDto> menuList(String menuName) {
        List<MenuDto> menuDtos = userMapper.menuList(menuName);
        List<MenuDto> menuDtoList = menuTree(menuDtos);
        return menuDtoList;
    }

    @Override
    public void addMenu(MenuAddParam param) throws Exception {
        UmMenu menu = new UmMenu();
        BeanUtils.copyProperties(param, menu);
        menuService.save(menu);
    }

    @Override
    public void updateMenu(MenuEditParam param) throws Exception {
        Long id = param.getId();
        delMenuCorrelation(id);
        UmMenu menu = new UmMenu();
        BeanUtils.copyProperties(param, menu);
        menuService.updateById(menu);
    }

    @Override
    @Transactional
    public void delMenu(Long[] menuId) throws Exception {
        for (Long menuIds : menuId) {
            LambdaQueryWrapper<UmRoleMenu> removeMenuWrapper = new LambdaQueryWrapper<>();
            removeMenuWrapper.eq(UmRoleMenu::getMenuId, menuIds);
            delMenuCorrelation(menuIds);
            roleMenuService.remove(removeMenuWrapper);
        }
        menuService.removeByIds(menuId);
    }

    private void delMenuCorrelation(Long menuId) {
        LambdaQueryWrapper<UmRoleMenu> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(UmRoleMenu::getMenuId, menuId);
        List<UmRoleMenu> list = roleMenuService.list(wrapper);
        List<Long> collect = list.stream().map(v -> v.getRoleId()).collect(Collectors.toList());
        userCacheService.delResourceListByRole(collect);
    }

    @Override
    public List<MenuDto> roleMenus(Long roleId) {
        List<MenuDto> menuDtoList = userMapper.menuList(null);
        List<MenuDto> menuDtoListRole = userMapper.menuListByRoleId(roleId);
        menuDtoList.stream().forEach(v -> {
            boolean b = menuDtoListRole.stream().anyMatch(c -> c.getId() == v.getId());
            if (b) {
                v.setChecked(true);
            } else {
                v.setChecked(false);
            }
        });
        List<MenuDto> menuTree = menuTree(menuDtoList);
        return menuTree;
    }

    @Override
    @Transactional
    public void updateRoleMenus(MenuParam param) {
        Long roleId = param.getRoleId();
        List<MenuDto> menus = param.getMenus();
        List<UmRoleMenuDto> UmRoleMenuDtos = new ArrayList<>();
        treeUpdateRoleMenus(menus, roleId, UmRoleMenuDtos);
        LambdaQueryWrapper<UmRoleMenu> removeWrapper = new LambdaQueryWrapper<>();
        removeWrapper.eq(UmRoleMenu::getRoleId, roleId);
        roleMenuService.remove(removeWrapper);
        roleMenuService.saveDtoBatch(UmRoleMenuDtos);
        userCacheService.delResourceList(roleId);
    }

    private static List<UmRoleMenuDto> treeUpdateRoleMenus(List<MenuDto> menuDtos, Long roleId, List<UmRoleMenuDto> UmRoleMenuDtos) {

        menuDtos.stream().forEach(v -> {
            Boolean checked = v.getChecked();
            if (checked) {
                UmRoleMenuDto UmRoleMenuDto = new UmRoleMenuDto();
                UmRoleMenuDto.setMenuId(v.getId());
                UmRoleMenuDto.setRoleId(roleId);
                UmRoleMenuDtos.add(UmRoleMenuDto);
            }
            List<MenuDto> children = v.getChildren();
            if (CollectionUtils.isNotEmpty(children)) {
                treeUpdateRoleMenus(children, roleId, UmRoleMenuDtos);
            }
        });
        return UmRoleMenuDtos;

    }


    List<MenuDto> menuTree(List<MenuDto> menuDtos) {
        List<MenuDto> rootMenus = menuDtos.stream().filter(v -> v.getParentMenuId() == 0).collect(Collectors.toList());
        rootMenus.stream().forEach(v -> {
            List<MenuDto> child = getChild(v.getId(), menuDtos);
            if (child.size() == 0) return;
            v.setChildren(child);
        });
        return rootMenus;
    }

    private static List<MenuDto> getChild(Long id, List<MenuDto> menuDtos) {
        List<MenuDto> childList = new ArrayList<>();
        menuDtos.stream().forEach(v -> {
            if (id == v.getParentMenuId()) {
                childList.add(v);
            }
        });
        childList.stream().forEach(v -> {
            List<MenuDto> child = getChild(v.getId(), menuDtos);
            v.setChildren(child);
        });
        if (childList.size() == 0) {
            return new ArrayList<>();
        }
        return childList;
    }
}
