package cn.getech.poros.permission.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import cn.getech.poros.framework.common.annotation.Excel;
import java.util.Date;


/**
 * <pre>
 *  返回数据模型
 * </pre>
 *
 * <AUTHOR>
 * @date 2022-08-15
 */
@Data
@ApiModel(value = "UmUserRoleDto", description = "返回数据模型")
public class UmUserRoleDto{


    @ApiModelProperty(value = "roleId")
    @Excel(name="roleId",cellType = Excel.ColumnType.STRING )
    private Long roleId;

    @ApiModelProperty(value = "userId")
    @Excel(name="userId",cellType = Excel.ColumnType.STRING )
    private Long userId;

    @ApiModelProperty(value = "")
    @Excel(name="",cellType = Excel.ColumnType.STRING)
    private Long id;

}