package cn.getech.poros.permission.service.impl;

import cn.getech.poros.permission.entity.UmRoleMenu;
import cn.getech.poros.permission.mapper.UmRoleMenuMapper;
import cn.getech.poros.permission.service.IUmRoleMenuService;
import cn.getech.poros.framework.common.service.impl.BaseServiceImpl;
import cn.getech.poros.permission.dto.UmRoleMenuQueryParam;
import cn.getech.poros.permission.dto.UmRoleMenuAddParam;
import cn.getech.poros.permission.dto.UmRoleMenuEditParam;
import cn.getech.poros.permission.dto.UmRoleMenuParamMapper;
import cn.getech.poros.permission.dto.UmRoleMenuDto;
import cn.getech.poros.framework.common.bean.BaseEntity;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import cn.getech.poros.framework.common.api.PageResult;
import cn.getech.poros.framework.common.api.Assert;
import cn.getech.poros.framework.common.api.ResultCode;
import java.util.Optional;
import java.util.List;


/**
 * <pre>
 *  服务实现类
 * </pre>
 *
 * <AUTHOR>
 * @since 2022-08-15
 */
@Slf4j
@Service
public class UmRoleMenuServiceImpl extends BaseServiceImpl<UmRoleMenuMapper, UmRoleMenu> implements IUmRoleMenuService {

    @Autowired
    private UmRoleMenuParamMapper umRoleMenuParamMapper;

    @Override
    public PageResult<UmRoleMenuDto> pageDto(UmRoleMenuQueryParam umRoleMenuQueryParam) {
        Wrapper<UmRoleMenu> wrapper = getPageSearchWrapper(umRoleMenuQueryParam);
        PageResult<UmRoleMenuDto> result = umRoleMenuParamMapper.pageEntity2Dto(page(umRoleMenuQueryParam, wrapper));

        return Optional.ofNullable(result).orElse(new PageResult<>());
    }

    @SuppressWarnings("unchecked")
    @Override
    public boolean saveByParam(UmRoleMenuAddParam umRoleMenuAddParam) {
        UmRoleMenu umRoleMenu = umRoleMenuParamMapper.addParam2Entity(umRoleMenuAddParam);
        Assert.notNull(ResultCode.PARAM_VALID_ERROR,umRoleMenu);
        return save(umRoleMenu);
    }

    @SuppressWarnings("unchecked")
    @Override
    public boolean updateByParam(UmRoleMenuEditParam umRoleMenuEditParam) {
        UmRoleMenu umRoleMenu = umRoleMenuParamMapper.editParam2Entity(umRoleMenuEditParam);
        Assert.notNull(ResultCode.PARAM_VALID_ERROR,umRoleMenu);
        return updateById(umRoleMenu);
    }


    @Override
    public UmRoleMenuDto getDtoById(Long id) {
        return umRoleMenuParamMapper.entity2Dto((UmRoleMenu) this.getById(id));
    }

    @SuppressWarnings("unchecked")
    @Override
    public boolean saveDtoBatch(List<UmRoleMenuDto> rows) {
        return saveBatch(umRoleMenuParamMapper.dtoList2Entity(rows));
    }

    private Wrapper<UmRoleMenu> getPageSearchWrapper(UmRoleMenuQueryParam umRoleMenuQueryParam) {
        LambdaQueryWrapper<UmRoleMenu> wrapper = Wrappers.<UmRoleMenu>lambdaQuery();
        if(BaseEntity.class.isAssignableFrom(UmRoleMenu.class)){
            wrapper.orderByDesc(UmRoleMenu::getUpdateTime,UmRoleMenu::getCreateTime);
        }
        return wrapper;
    }
}
