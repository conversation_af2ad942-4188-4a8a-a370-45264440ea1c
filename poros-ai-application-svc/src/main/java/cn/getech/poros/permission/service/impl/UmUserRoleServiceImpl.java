package cn.getech.poros.permission.service.impl;

import cn.getech.poros.permission.entity.UmUserRole;
import cn.getech.poros.permission.mapper.UmUserRoleMapper;
import cn.getech.poros.permission.service.IUmUserRoleService;
import cn.getech.poros.framework.common.service.impl.BaseServiceImpl;
import cn.getech.poros.permission.dto.UmUserRoleQueryParam;
import cn.getech.poros.permission.dto.UmUserRoleAddParam;
import cn.getech.poros.permission.dto.UmUserRoleEditParam;
import cn.getech.poros.permission.dto.UmUserRoleParamMapper;
import cn.getech.poros.permission.dto.UmUserRoleDto;
import cn.getech.poros.framework.common.bean.BaseEntity;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import cn.getech.poros.framework.common.api.PageResult;
import cn.getech.poros.framework.common.api.Assert;
import cn.getech.poros.framework.common.api.ResultCode;
import java.util.Optional;
import java.util.List;


/**
 * <pre>
 *  服务实现类
 * </pre>
 *
 * <AUTHOR>
 * @since 2022-08-15
 */
@Slf4j
@Service
public class UmUserRoleServiceImpl extends BaseServiceImpl<UmUserRoleMapper, UmUserRole> implements IUmUserRoleService {

    @Autowired
    private UmUserRoleParamMapper umUserRoleParamMapper;

    @Override
    public PageResult<UmUserRoleDto> pageDto(UmUserRoleQueryParam umUserRoleQueryParam) {
        Wrapper<UmUserRole> wrapper = getPageSearchWrapper(umUserRoleQueryParam);
        PageResult<UmUserRoleDto> result = umUserRoleParamMapper.pageEntity2Dto(page(umUserRoleQueryParam, wrapper));

        return Optional.ofNullable(result).orElse(new PageResult<>());
    }

    @SuppressWarnings("unchecked")
    @Override
    public boolean saveByParam(UmUserRoleAddParam umUserRoleAddParam) {
        UmUserRole umUserRole = umUserRoleParamMapper.addParam2Entity(umUserRoleAddParam);
        Assert.notNull(ResultCode.PARAM_VALID_ERROR,umUserRole);
        return save(umUserRole);
    }

    @SuppressWarnings("unchecked")
    @Override
    public boolean updateByParam(UmUserRoleEditParam umUserRoleEditParam) {
        UmUserRole umUserRole = umUserRoleParamMapper.editParam2Entity(umUserRoleEditParam);
        Assert.notNull(ResultCode.PARAM_VALID_ERROR,umUserRole);
        return updateById(umUserRole);
    }


    @Override
    public UmUserRoleDto getDtoById(Long id) {
        return umUserRoleParamMapper.entity2Dto((UmUserRole) this.getById(id));
    }

    @SuppressWarnings("unchecked")
    @Override
    public boolean saveDtoBatch(List<UmUserRoleDto> rows) {
        return saveBatch(umUserRoleParamMapper.dtoList2Entity(rows));
    }

    private Wrapper<UmUserRole> getPageSearchWrapper(UmUserRoleQueryParam umUserRoleQueryParam) {
        LambdaQueryWrapper<UmUserRole> wrapper = Wrappers.<UmUserRole>lambdaQuery();
        if(BaseEntity.class.isAssignableFrom(UmUserRole.class)){
            wrapper.orderByDesc(UmUserRole::getUpdateTime,UmUserRole::getCreateTime);
        }
        return wrapper;
    }
}
