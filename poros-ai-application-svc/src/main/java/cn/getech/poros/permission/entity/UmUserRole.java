package cn.getech.poros.permission.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import cn.getech.poros.framework.common.bean.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import cn.getech.poros.framework.common.annotation.Excel;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-15
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("um_user_role")
public class UmUserRole extends BaseEntity {

    private static final long serialVersionUID=1L;

    @TableField("role_id")
    private Long roleId;

    @TableField("user_id")
    private Long userId;


}
