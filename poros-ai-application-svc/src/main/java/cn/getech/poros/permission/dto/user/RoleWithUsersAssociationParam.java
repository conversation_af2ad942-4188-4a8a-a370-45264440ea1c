package cn.getech.poros.permission.dto.user;

import cn.getech.poros.framework.common.param.ApiParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <pre>
 *  新增参数
 * </pre>
 *
 * <AUTHOR>
 * @date 2022-06-08
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "RoleWithUsersAssociationParam", description = "角色关联用户入参")
public class RoleWithUsersAssociationParam extends ApiParam {

    @ApiModelProperty(value = "角色id")
    private Long roleId;

    private List<Long> userIds;
}