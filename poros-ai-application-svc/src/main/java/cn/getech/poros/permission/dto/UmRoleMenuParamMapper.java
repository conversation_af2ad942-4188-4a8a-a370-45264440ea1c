package cn.getech.poros.permission.dto;

import cn.getech.poros.framework.common.bean.BaseEntity;
import cn.getech.poros.permission.entity.UmRoleMenu;
import org.mapstruct.Mapper;
import cn.getech.poros.framework.common.api.PageResult;
import java.util.List;

/**
 * <pre>
 *  参数实体映射
 * </pre>
 *
 * <AUTHOR>
 * @date 2022-08-15
 */
@Mapper(componentModel = "spring",imports = {BaseEntity.class})
public interface  UmRoleMenuParamMapper{

    /**
     * 新增参数转换为实体
     *
     * @param umRoleMenuAddParam
     * @return
     */
    UmRoleMenu addParam2Entity(UmRoleMenuAddParam umRoleMenuAddParam);

    /**
     * 编辑参数转换为实体
     * @param umRoleMenuEditParam
     * @return
     */
    UmRoleMenu editParam2Entity(UmRoleMenuEditParam umRoleMenuEditParam);

    /**
     * 实体转换为Dto
     * @param umRoleMenu
     * @return
     */
    UmRoleMenuDto entity2Dto(UmRoleMenu umRoleMenu);

    /**
     * 分页实体转DTO
     * @param page
     * @return
     */
    PageResult<UmRoleMenuDto> pageEntity2Dto(PageResult<UmRoleMenu> page);


    /**
     * dto集合转entity集合
     * @param rows
     * @return
     */
    List<UmRoleMenu> dtoList2Entity(List<UmRoleMenuDto> rows);

}
