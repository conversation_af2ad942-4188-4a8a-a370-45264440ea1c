package cn.getech.poros.permission.service.impl;

import cn.getech.poros.permission.dto.user.MenuDto;
import cn.getech.poros.permission.dto.user.UserDto;
import cn.getech.poros.permission.service.RedisService;
import cn.getech.poros.permission.service.UserCacheService;
import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 后台用户缓存操作Service实现类
 */
@Service
public class UserCacheServiceImpl implements UserCacheService {

    @Autowired
    private RedisService redisService;

    @Value("${redis.database}")
    private String REDIS_DATABASE;
    @Value("${redis.expire.common}")
    private Long REDIS_EXPIRE;
    @Value("${redis.key.admin}")
    private String REDIS_KEY_ADMIN;
    @Value("${redis.key.resourceList}")
    private String REDIS_KEY_RESOURCE_LIST;
    @Value("${redis.key.token}")
    private String REDIS_KEY_TOKEN;

    @Override
    public void delUser(String userName) {
            String key = REDIS_DATABASE + ":" + REDIS_KEY_ADMIN + ":" + userName;
            redisService.del(key);
    }

    @Override
    public void delResourceList(Long roleId) {
        String key = REDIS_DATABASE + ":" + REDIS_KEY_RESOURCE_LIST + ":" + roleId;
        redisService.del(key);
    }

    @Override
    public void delUsersListByRole(List<String> userNames) {
        if (CollUtil.isNotEmpty(userNames)) {
            String keyPrefix = REDIS_DATABASE + ":" + REDIS_KEY_ADMIN + ":";
            List<String> keys = userNames.stream().map(relation -> keyPrefix + relation).collect(Collectors.toList());
            redisService.del(keys);
        }
    }

    @Override
    public void delResourceListByRole(List<Long> roleIds) {
        if (CollUtil.isNotEmpty(roleIds)) {
            String keyPrefix = REDIS_DATABASE + ":" + REDIS_KEY_RESOURCE_LIST + ":";
            List<String> keys = roleIds.stream().map(relation -> keyPrefix + relation).collect(Collectors.toList());
            redisService.del(keys);
        }
    }

    @Override
    public String getToken(String username) {
        String key = REDIS_DATABASE + ":" + REDIS_KEY_TOKEN + ":" + username;
        String result = (String) redisService.get(key);
        return result;
    }

    @Override
    public void setToken(String username, String token) {
        String key = REDIS_DATABASE + ":" + REDIS_KEY_TOKEN + ":" + username;
        redisService.set(key, token, REDIS_EXPIRE);
    }


    @Override
    public UserDto getUser(String username) {
        String key = REDIS_DATABASE + ":" + REDIS_KEY_ADMIN + ":" + username;
        Object o = redisService.get(key);
        UserDto userDto = JSON.parseObject((String) o, new TypeReference<UserDto>() {
        });
        return userDto;
    }

    @Override
    public void setUser(UserDto userDto) {
        String key = REDIS_DATABASE + ":" + REDIS_KEY_ADMIN + ":" + userDto.getUserName();
        redisService.set(key, JSON.toJSONString(userDto), REDIS_EXPIRE);
    }

    @Override
    public List<MenuDto> getResourceList(Long roleId) {
        String key = REDIS_DATABASE + ":" + REDIS_KEY_RESOURCE_LIST + ":" + roleId;
        Object o = redisService.get(key);
        List<MenuDto> menuDtoList = JSON.parseObject((String) o, new TypeReference<List<MenuDto>>() {
        });
        return menuDtoList;
    }

    @Override
    public void setResourceList(Long roleId, List<MenuDto> resourceList) {
        String key = REDIS_DATABASE + ":" + REDIS_KEY_RESOURCE_LIST + ":" + roleId;
        redisService.set(key, JSON.toJSONString(resourceList), REDIS_EXPIRE);
    }
}
