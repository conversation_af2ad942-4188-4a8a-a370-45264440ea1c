package cn.getech.poros.permission.dto;

import cn.getech.poros.framework.common.bean.BaseEntity;
import cn.getech.poros.permission.entity.UmUser;
import org.mapstruct.Mapper;
import cn.getech.poros.framework.common.api.PageResult;
import java.util.List;

/**
 * <pre>
 *  参数实体映射
 * </pre>
 *
 * <AUTHOR>
 * @date 2022-11-04
 */
@Mapper(componentModel = "spring",imports = {BaseEntity.class})
public interface  UmUserParamMapper{

    /**
     * 新增参数转换为实体
     *
     * @param umUserAddParam
     * @return
     */
    UmUser addParam2Entity(UmUserAddParam umUserAddParam);

    /**
     * 编辑参数转换为实体
     * @param umUserEditParam
     * @return
     */
    UmUser editParam2Entity(UmUserEditParam umUserEditParam);

    /**
     * 实体转换为Dto
     * @param umUser
     * @return
     */
    UmUserDto entity2Dto(UmUser umUser);

    /**
     * 分页实体转DTO
     * @param page
     * @return
     */
    PageResult<UmUserDto> pageEntity2Dto(PageResult<UmUser> page);


    /**
     * dto集合转entity集合
     * @param rows
     * @return
     */
    List<UmUser> dtoList2Entity(List<UmUserDto> rows);

}
