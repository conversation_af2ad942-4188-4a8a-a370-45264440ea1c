package cn.getech.poros.permission.dto.user;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;


/**
 * <pre>
 *  返回数据模型
 * </pre>
 *
 * <AUTHOR>
 * @date 2022-06-07
 */
@Data
@ApiModel(value = "UserDto", description = "返回数据模型")
public class UserDto implements Serializable {

    @ApiModelProperty(value = "userCode")
    private String userCode;

    @ApiModelProperty(value = "userName")
    private String userName;

    @ApiModelProperty(value = "卡号")
    private String idCardNo;

    @ApiModelProperty(value = "等级")
    private Integer level;

    @ApiModelProperty(value = "userAccount")
    private String userAccount;

    @ApiModelProperty(value = "userPassword")
    private String userPassword;

    @ApiModelProperty(value = "")
    private Long id;

    @ApiModelProperty(value = "")
    private Long roleId;
}