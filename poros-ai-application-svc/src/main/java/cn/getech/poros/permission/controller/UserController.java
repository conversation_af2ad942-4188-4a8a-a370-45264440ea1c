package cn.getech.poros.permission.controller;



import cn.getech.poros.permission.dto.user.*;
import cn.getech.poros.permission.enums.ErrorEnum;
import cn.getech.poros.permission.service.UserService;
import cn.getech.poros.framework.common.api.PageResult;
import cn.getech.poros.framework.common.api.RestResponse;
import cn.getech.poros.framework.common.auditlog.AuditLog;
import cn.getech.poros.framework.common.auditlog.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

@Slf4j
@RestController
@RequestMapping("/user")
@Api(tags = "用户服务接口")
public class UserController {

    @Resource
    private UserService userService;

    @ApiOperation("用户-注册")
    @AuditLog(title = "注册用户", desc = "注册用户", businessType = BusinessType.INSERT)
    @PostMapping("/register")
    public RestResponse<Boolean> register(@RequestBody @Valid UserAddParam param) {
        try {
            userService.register(param);
        } catch (Exception e) {
            log.error("register error:", e);
            return RestResponse.failed(e.getMessage());
        }
        return RestResponse.ok();
    }

    @ApiOperation("用户-登录")
    @PostMapping("/login")
    public RestResponse<String> login(@RequestBody @Valid UserLoginParam param) {
        String login = null;
        try {
            login = userService.login(param);
        } catch (Exception e) {
            log.error("login error:", e);
            return RestResponse.failed(e.getMessage());
        }
        return RestResponse.ok(login);
    }

    @ApiOperation(value = "用户-登出")
    @GetMapping(value = "/logout")
    public RestResponse logout() {
        try {
            userService.logout();
        } catch (Exception e) {
            log.error("logout error:", e);
            return RestResponse.failed(e.getMessage());
        }
        return RestResponse.ok();
    }

    @ApiOperation("用户-获取当前用户信息")
    @GetMapping("/getUserInfo")
    public RestResponse<UserDetailsDto> getUserInfo() {
        UserDetailsDto userDetailsDto;
        try {
            userDetailsDto = userService.getUserInfo();
        } catch (Exception e) {
            log.error("login error:", e);
            return RestResponse.failed(e.getMessage());
        }
        return RestResponse.ok(userDetailsDto);
    }

    @ApiOperation("用户-列表")
    @GetMapping("/userList")
    public RestResponse<PageResult<UserListDto>> userList(@Valid UserListParam param, @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum, @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize) {
        PageResult<UserListDto> userListDtoPageResult;
        try {
            userListDtoPageResult = userService.userList(param, pageNum, pageSize);
        } catch (Exception e) {
            log.error("userList error:", e);
            return RestResponse.failed(e.getMessage());
        }
        return RestResponse.ok(userListDtoPageResult);
    }

    @ApiOperation("用户-编辑")
    @AuditLog(title = "编辑用户信息", desc = "编辑用户信息", businessType = BusinessType.UPDATE)
    @PostMapping("/editUser")
    public RestResponse<Boolean> editUser(@RequestBody @Valid UserEditParam param) {
        try {
            userService.editUser(param);
        } catch (Exception e) {
            log.error("editUser error:", e);
            return RestResponse.failed(e.getMessage());
        }
        return RestResponse.ok();
    }

    @ApiOperation("用户-修改密码")
    @AuditLog(title = "修改用户密码", desc = "修改用户密码", businessType = BusinessType.UPDATE)
    @PostMapping("/updateUserPassword")
    public RestResponse<Boolean> updateUserPassword(@RequestBody @Valid UserEditPasswordParam param) {
        try {
            userService.updateUserPassword(param);
        } catch (Exception e) {
            log.error("editUser error:", e);
            return RestResponse.failed(e.getMessage());
        }
        return RestResponse.ok();
    }

    @ApiOperation(value="用户-删除")
    @PostMapping("/delUser")
    public RestResponse<Boolean> delUser(@RequestBody Long[] userIds) {
        try {
            userService.delUser(userIds);
        } catch (Exception e) {
            log.error("delUser error:",e);
        }
        return RestResponse.ok();
    }


    @ApiOperation("关联用户-编辑")
    @PostMapping("/roleWithUsersAssociation")
    public RestResponse<Boolean> roleWithUsersAssociation(@RequestBody @Valid RoleWithUsersAssociationParam param) {
        try {
            userService.roleWithUsersAssociation(param);
        } catch (Exception e) {
            log.error("editUser error:", e);
            return RestResponse.failed(e.getMessage());
        }
        return RestResponse.ok();
    }



    @ApiOperation("角色-列表")
    @GetMapping("/roleList")
    public RestResponse<PageResult<RoleListDto>> roleList(@Valid RoleListParam param) {
        PageResult<RoleListDto> result;
        try {
            result = userService.roleList(param);
        } catch (Exception e) {
            log.error("roleList error:", e);
            return RestResponse.failed(e.getMessage());
        }
        return RestResponse.ok(result);
    }

    @ApiOperation(value="角色-新增")
    @PostMapping("/saveRole")
    public RestResponse<Boolean> saveRole(@RequestBody RoleAddParam param) {
        try {
            userService.saveRole(param);
        } catch (Exception e) {
            log.error("saveRole error:",e);
        }
        return RestResponse.ok();
    }

    @ApiOperation(value="角色-更新")
    @PostMapping("/updateRole")
    public RestResponse<Boolean> updateRole(@RequestBody RoleEditParam param) {
        try {
            userService.updateRole(param);
        } catch (Exception e) {
            log.error("updateRole error:",e);
        }
        return RestResponse.ok();
    }

    @ApiOperation(value="角色-删除")
    @PostMapping("/delRole")
    public RestResponse<Boolean> delRole(@RequestBody Long[] roleIds) {
        try {
            userService.delRole(roleIds);
        } catch (Exception e) {
            log.error("delRole error:",e);
        }
        return RestResponse.ok();
    }


    @ApiOperation(value="分配资源")
    @GetMapping("/roleMenus")
    public RestResponse<List<MenuDto>> roleMenus(Long roleId) {
        if (roleId == null){
            RestResponse.failed(ErrorEnum.PARAM_NOTNULL.getRelType());
        }
        try {
           return RestResponse.ok(userService.roleMenus(roleId));
        } catch (Exception e) {
            log.error("menuList error:",e);
            return RestResponse.failed(e.getMessage());
        }
    }

    @ApiOperation(value="分配资源-更新")
    @PostMapping("/updateRoleMenus")
    public RestResponse<Boolean> updateRoleMenus(@RequestBody MenuParam param) {
        if (param == null || param.getRoleId() == null || param.getMenus().size() == 0){
            RestResponse.failed(ErrorEnum.PARAM_NOTNULL.getRelType());
        }
        try {
            userService.updateRoleMenus(param);
        } catch (Exception e) {
            log.error("menuList error:",e);
            return RestResponse.failed(e.getMessage());
        }
        return RestResponse.ok();
    }

    @ApiOperation(value="菜单-列表")
    @GetMapping("/menuList")
    public RestResponse<List<MenuDto>> menuList(String menuName) {
        try {
            return RestResponse.ok(userService.menuList(menuName));
        } catch (Exception e) {
            log.error("menuList error:",e);
            return RestResponse.failed(e.getMessage());
        }
    }

    @ApiOperation("菜单-新增")
    @AuditLog(title = "菜单",desc = "新增菜单",businessType = BusinessType.INSERT)
    @PostMapping("/addMenu")
    public RestResponse<Boolean> addMenu(@RequestBody @Valid MenuAddParam param) {
        try {
            if (param == null
                    || StringUtils.isBlank(param.getMenuName())
                    || param.getParentMenuId() == null
            ){
                return RestResponse.failed(ErrorEnum.PARAM_NOTNULL.getRelType());
            }
            userService.addMenu(param);
        } catch (Exception e) {
            log.error("addMenu error:",e);
            return RestResponse.failed(e.getMessage());
        }
        return RestResponse.ok();
    }

    @ApiOperation("菜单-修改")
    @AuditLog(title = "菜单",desc = "修改菜单",businessType = BusinessType.UPDATE)
    @PostMapping("/updateMenu")
    public RestResponse<Boolean> updateMenu(@RequestBody @Valid MenuEditParam smtMenuEditParam) {
        try {
            if (smtMenuEditParam == null
                    || StringUtils.isBlank(smtMenuEditParam.getMenuName())
                    || smtMenuEditParam.getParentMenuId() == null
                    || smtMenuEditParam.getMenuCode() == null
            ){
                return RestResponse.failed(ErrorEnum.PARAM_NOTNULL.getRelType());
            }
            userService.updateMenu(smtMenuEditParam);
        } catch (Exception e) {
            log.error("updateMenu error:",e);
            return RestResponse.failed(e.getMessage());
        }
        return RestResponse.ok();
    }

    @ApiOperation(value="菜单-删除")
    @PostMapping("/delMenu")
    public RestResponse<Boolean> delMenu(@RequestBody Long[] menuId) {
        try {
            userService.delMenu(menuId);
        } catch (Exception e) {
            log.error("delMenu error:",e);
        }
        return RestResponse.ok();
    }
}
