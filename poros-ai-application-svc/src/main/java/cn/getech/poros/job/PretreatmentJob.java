package cn.getech.poros.job;


import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 定时任务
 *
 * <AUTHOR>
 * @date 2021-09-16
 */
@Component
@Slf4j
public class PretreatmentJob {



    /**
     * 统计月份数据，每天凌晨一天跑一次
     */
//    @Scheduled(cron = "0/10 * * * * ?")
    @Scheduled(cron = "0 0 1 * * ?")
    public void getReportFormMonthInfo() {

    }
    /**
     * 定时删除panel历史记录
     */
    @Scheduled(cron = "0 0 19 * * ?")
    public void deletePanelInfoJob() {
    }
}
