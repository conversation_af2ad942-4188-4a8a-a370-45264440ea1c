package cn.getech.poros;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * <AUTHOR>
 * @date 2020/2/19
 */
@EnableScheduling
@SpringBootApplication
@EnableDiscoveryClient
//@EnableFeignClients(basePackages = "cn.getech.poros")
@MapperScan("cn.getech.poros.**.mapper")
public class  Application {
    public static void main(String[] args) {
        System.err.println("2022-07-07");
        SpringApplication.run(Application.class, args);
    }
}
