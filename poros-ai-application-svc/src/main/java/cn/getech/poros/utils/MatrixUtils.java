package cn.getech.poros.utils;



import java.math.BigDecimal;


public class MatrixUtils {

    public static String calcRate(Integer num, Integer total) {
        BigDecimal rate = BigDecimal.ZERO;
        if (num != null && total != null && total > 0) {
            rate = BigDecimal.valueOf(num).divide(BigDecimal.valueOf(total), 10, BigDecimal.ROUND_HALF_UP);
        }
        return rate.setScale(2, BigDecimal.ROUND_HALF_UP).stripTrailingZeros().toPlainString();
    }
    public static String calcRate(BigDecimal num, Long total) {
        BigDecimal rate = BigDecimal.ZERO;
        if (num != null && total != null && total > 0) {
            rate = BigDecimal.valueOf(num.doubleValue()).divide(BigDecimal.valueOf(total), 10, BigDecimal.ROUND_HALF_UP);
        }
        return rate.setScale(2, BigDecimal.ROUND_HALF_UP).stripTrailingZeros().toPlainString();
    }
    public static String calcLongRate(Long num, Long total) {
        BigDecimal rate = BigDecimal.ZERO;
        if (num != null && total != null && total > 0) {
            rate = BigDecimal.valueOf(num).divide(BigDecimal.valueOf(total), 10, BigDecimal.ROUND_HALF_UP);
        }
        return rate.setScale(2, BigDecimal.ROUND_HALF_UP).stripTrailingZeros().toPlainString();
    }
    public static String calcRate(Double num, Long total) {
        BigDecimal rate = BigDecimal.ZERO;
        if (num != null && total != null && total > 0) {
            rate = BigDecimal.valueOf(num).divide(BigDecimal.valueOf(total), 10, BigDecimal.ROUND_HALF_UP);
        }
        return rate.setScale(2, BigDecimal.ROUND_HALF_UP).stripTrailingZeros().toPlainString();
    }


}