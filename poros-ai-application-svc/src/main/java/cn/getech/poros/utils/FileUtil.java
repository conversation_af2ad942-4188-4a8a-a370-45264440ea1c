package cn.getech.poros.utils;

import com.alibaba.fastjson.util.IOUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@Component
public class FileUtil {

    final static String encoding = "UTF-8";
    private static String upload;

    @Value(value = "${nfs.upload}")
    public void setUpload(String upload) {
        this.upload = upload;
    }


    public static boolean isFolder(String fileName) {
        File file = new File(fileName);
        if (file.isDirectory()) {
            return true;
        }
        return false;
    }

    /**
     * @param file
     * @return 文件上传方法
     * @throws IOException
     */
    public static String uploadFile(MultipartFile file, String fileName) throws IOException {

        // 文件原名称

        File targetFile = new File(upload, fileName);
        if (targetFile.exists() == false) {
            targetFile.mkdirs();
        } else {
            //如果文件已存在，先删除再创建
            targetFile.delete();
            targetFile.mkdirs();
        }
        // 转存文件到指定的路径
        file.transferTo(targetFile);
        return fileName;
    }


    /**
     * 功能：Java读取txt文件的内容
     * 步骤：1：先获得文件句柄
     * 2：获得文件句柄当做是输入一个字节码流，需要对这个输入流进行读取
     * 3：读取到输入流后，需要读取生成字节流
     * 4：一行一行的输出。readline()。
     * 备注：需要考虑的是异常情况
     *
     * @param filePath
     */
    public static List<String> readTxtFile(String filePath) throws IOException {

        InputStreamReader read = null;
        try {

            File file = new File(filePath);
            List<String> list = new ArrayList<>();
            //判断文件是否存在
            if (file.isFile() == false && file.exists() == false) {
                return new ArrayList<>();
            }
            read = new InputStreamReader(new FileInputStream(file), encoding);//考虑到编码格式
            BufferedReader bufferedReader = new BufferedReader(read);
            String lineTxt = null;
            while ((lineTxt = bufferedReader.readLine()) != null) {
                list.add(lineTxt);
            }
            return list;

        } catch (Exception e) {
            log.error("文件读取异常", e);
        } finally {
            read.close();
        }
        return new ArrayList<>();
    }


    /**
     * 复制文件
     *
     * @param oldFile
     * @param newFile
     * @return
     */
    public static boolean copyFile(String oldFile, String newFile) {
        //定义输入输出流
        InputStream is = null;
        OutputStream os = null;
        try {
            File file = new File(newFile);
            if (!file.getParentFile().exists()) {
                file.getParentFile().mkdirs();
            }
            //读取
            is = new FileInputStream(oldFile);
            //输出
            os = new FileOutputStream(newFile);

            //定义字节缓冲数组
            byte[] flush = new byte[1024];
            //定义读取长度
            int len;
            while ((len = is.read(flush)) != -1) {
                os.write(flush, 0, len);
            }
            os.flush();
        } catch (FileNotFoundException e) {
            log.error("FileNotFoundException: caused by:" + e.getMessage());
        } catch (IOException e) {
            log.error("IOException: caused by:" + e.getMessage());
        } finally {
            //先开启的流 后关闭
            if (os != null)
                IOUtils.close(os);
            if (is != null)
                IOUtils.close(is);
        }
        if (new File(newFile).exists()) {
            return true;
        }
        return false;
    }


    /**
     * 批量复制
     *
     * @param oldFile
     * @param newFile
     * @return
     */
    public static boolean copyFiles(String oldFile, String newFile) {
        try {
            File oldName = new File(oldFile);
            File newName = new File(newFile);
            if (!oldName.isDirectory()) {
                return false;
            }
            //如果目的目录路径不存在，则进行创建
            if (!newName.isDirectory()) {
                newName.mkdirs();
                log.info("copyFiles newParentFile is not exists >>>>>>>>>>>[{}]", newName);
            }
            File[] files = oldName.listFiles();
            for (File fileList : files) {
                Path newChiFile = Paths.get(newName.getAbsolutePath() + File.separator + fileList.getName());
                Path oldFiel = Paths.get(fileList.getAbsolutePath());
                Files.copy(oldFiel, newChiFile);
            }

            return true;
        } catch (Exception e) {
            log.error("IOException: copyFiles:" + e.getMessage());
            return false;
        }
    }


    /**
     * 批量复制
     *
     * @param oldFile
     * @param newFile
     * @return
     */
    public static boolean copyFileImgNoReplace(String oldFile, String newFile) {
        try {
            File oldName = new File(oldFile);
            File newName = new File(newFile);
            if (!oldName.exists()) {
                return false;
            }
            //要拷贝的目的文件已存在则直接返回成功
            if (newName.exists()) {
                return true;
            }
            //如果目的目录路径不存在，则进行创建
            if (!newName.getParentFile().exists()) {
                newName.getParentFile().mkdirs();
            }
            Path newFiel = Paths.get(newFile);
            Path oldFiel = Paths.get(oldFile);
            Files.copy(oldFiel, newFiel);
            return true;
        } catch (Exception e) {
            log.error("IOException: copyFileImg:" + e.getMessage());
            return false;
        }
    }


    /**
     * 移动文件夹及所有子文件
     *
     * @param oldFile
     * @param newFile
     * @return
     */
    public static boolean moveFiles(String oldFile, String newFile) {
        try {
            File oldName = new File(oldFile);
            File newName = new File(newFile);
            if (!oldName.isDirectory()) {
                return false;
            }
            //如果目的目录路径不存在，则进行创建
            if (!newName.isDirectory()) {
                newName.mkdirs();
                log.info("moveFile newParentFile is not exists >>>>>>>>>>>[{}]", newName);
            }
            File[] files = oldName.listFiles();
            for (File fileList : files) {
                File newChiFile = new File(newName.getAbsolutePath() + File.separator + fileList.getName());
                boolean b = fileList.renameTo(newChiFile);
                if (b == false) return false;
            }
            oldName.delete();
            return true;
        } catch (Exception e) {
            log.error("IOException: moveFile:" + e.getMessage());
            return false;
        }
    }


    /**
     * 删除单个文件
     *
     * @param fileName 被删除文件的文件名
     * @return 单个文件删除成功返回true, 否则返回false
     */
    public static boolean deleteFile(String fileName) {
        if (StringUtils.isEmpty(fileName)) {
            return false;
        }
        File file = new File(fileName);
        if (file.isFile() && file.exists()) {
            file.delete();
            return true;
        } else {
            return false;
        }
    }

    /**
     * 删除单个文件
     *
     * @param fileName 被删除文件的文件名
     * @return 单个文件删除成功返回true, 否则返回false
     */
    public static boolean deleteNasFile(String fileName) {
        fileName = upload + File.separator + fileName;
        File dirFile = new File(fileName);
        if (!dirFile.exists()) {
            return false;
        }

        if (dirFile.isFile()) {
            return dirFile.delete();
        } else {
            try {
                FileUtils.deleteDirectory(dirFile);
            } catch (IOException e) {
                log.error("deleteNasFile:", e);
                return false;
            }
            return true;
        }
    }

    /**
     * 获取文件名称的扩展名
     *
     * @param fileName
     * @return
     */
    public static String getFileExtension(String fileName) {
        String last = fileName.substring(fileName.lastIndexOf("."));
        return last;
    }


    public static void main(String[] args) {
       /* String name = "D:\\格力\\格力SMT—项目\\GREE\\JETAOI_Buffer3\\S07\\305001060102_v1.4\\20210412\\20210223091412";
        String names = "D:\\格力\\格力SMT—项目\\GREE\\JETAOI_Buffer3\\S07\\305001060102_v1.4\\20210412\\20210223091411122";
        try {
            boolean b = moveFiles(name, names);
            System.out.println(b);
        } catch (Exception e) {
            e.printStackTrace();
        }*/
        deleteNasFile("D:\\项目管理\\AOI\\recipe\\BB");
    }
}
