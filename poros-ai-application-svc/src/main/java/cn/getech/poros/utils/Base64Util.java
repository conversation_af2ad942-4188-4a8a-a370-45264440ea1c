package cn.getech.poros.utils;

import java.io.*;

import sun.misc.BASE64Decoder;
import sun.misc.BASE64Encoder;  
public class Base64Util
{


    public static final String SLIT_IMG_BASE64 = "base64,";
    //图片转化成base64字符串  
    public static String getImageStr(String imgFile)
    {//将图片文件转化为字节数组字符串，并对其进行Base64编码处理  
       //  imgFile = "";//待处理的图片
        InputStream in = null;  
        byte[] data = null;  
        //读取图片字节数组  
        try   
        {  
            in = new FileInputStream(imgFile);
            data = new byte[in.available()];  
            in.read(data);  
            in.close();  
        }   
        catch (IOException e)   
        {  
            e.printStackTrace();  
        }  
        //对字节数组Base64编码  
        BASE64Encoder encoder = new BASE64Encoder();  
        return encoder.encode(data);//返回Base64编码过的字节数组字符串  
    }  
      
    //base64字符串转化成图片  
    public static boolean generateImage(String imgStr,String dir) {
        String[] split = imgStr.split(SLIT_IMG_BASE64);
        imgStr = split[1];
        // String imgFile = "D:\\项目管理\\AOI\\img\\defect001.png";
        // imgStr = getImageStr(imgFile);
        //对字节数组字符串进行Base64解码并生成图片
        if (imgStr == null) //图像数据为空  
            return false;  
        BASE64Decoder decoder = new BASE64Decoder();  
        try   
        {  
            //Base64解码  
            byte[] b = decoder.decodeBuffer(imgStr);  
            for(int i=0;i<b.length;++i)  
            {  
                if(b[i]<0)  
                {//调整异常数据  
                    b[i]+=256;  
                }  
            }  
            //生成jpeg图片
            OutputStream out = new FileOutputStream(dir);
            out.write(b);  
            out.flush();  
            out.close();  
            return true;  
        }   
        catch (Exception e)   
        {  
            return false;  
        }  
    }

    public static void main(String[] args)
    {
      /*  String imgFile = "D:\\项目管理\\AOI\\img\\defect001.png";
        String strImg = getImageStr(imgFile);
        JsonUtil.writeFile("D:\\项目管理\\AOI\\img\\defect00111111.txt", strImg);
        System.out.println(strImg);
        String dir = "D:\\项目管理\\AOI\\img\\defect003.jpg";
        generateImage(strImg,dir);*/

    }
}