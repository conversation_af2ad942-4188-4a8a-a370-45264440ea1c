<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>poros-parent</artifactId>
        <groupId>cn.getech.poros</groupId>
        <version>0.9.4</version>
    </parent>
    <groupId>cn.getech.poros.ai</groupId>
    <modelVersion>4.0.0</modelVersion>
    <packaging>pom</packaging>
    <artifactId>poros-ai-application</artifactId>
    <version>1.0-SNAPSHOT</version>

    <properties>
        <poros.framework.version>0.9.4</poros.framework.version>
        <geek.api.version>1.0-SNAPSHOT</geek.api.version>
        <poros.api.version>1.0-SNAPSHOT</poros.api.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>cn.getech.poros.ai</groupId>
                <artifactId>poros-ai-application-api</artifactId>
                <version>${poros.api.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.getech.poros</groupId>
                <artifactId>poros-common</artifactId>
                <version>${poros.framework.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.getech.poros</groupId>
                <artifactId>poros-codegen</artifactId>
                <version>${poros.framework.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.getech.poros</groupId>
                <artifactId>poros-gateway</artifactId>
                <version>${poros.framework.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <modules>
        <module>poros-ai-application-api</module>
        <module>poros-ai-application-svc</module>
    </modules>

</project>